/**
 * Contact repository
 * 
 * Data access layer for contact operations
 */

import { Prisma } from '@prisma/client';
import { prisma } from '../index';
import { CreateContactData, UpdateContactData, ContactSearchCriteria, BulkContactOperation } from '../models/Contact';

/**
 * Contact repository class for database operations
 */
export class ContactRepository {
  /**
   * Find contact by ID with optional includes
   */
  async findById(id: string, includeRelations = true) {
    return prisma.contact.findUnique({
      where: { id },
      include: includeRelations ? {
        transaction: {
          select: {
            id: true,
            propertyAddress: true,
            status: true,
          },
        },
      } : false,
    });
  }

  /**
   * Create new contact
   */
  async create(contactData: CreateContactData) {
    return prisma.contact.create({
      data: contactData,
      include: {
        transaction: {
          select: {
            id: true,
            propertyAddress: true,
            status: true,
          },
        },
      },
    });
  }

  /**
   * Update contact by ID
   */
  async update(id: string, updateData: UpdateContactData) {
    return prisma.contact.update({
      where: { id },
      data: updateData,
      include: {
        transaction: {
          select: {
            id: true,
            propertyAddress: true,
            status: true,
          },
        },
      },
    });
  }

  /**
   * Delete contact by ID
   */
  async delete(id: string) {
    return prisma.contact.delete({
      where: { id },
    });
  }

  /**
   * Find contacts with search criteria and pagination
   */
  async findMany(criteria: ContactSearchCriteria, page = 1, limit = 10) {
    const skip = (page - 1) * limit;
    
    // Build where clause
    const where: Prisma.ContactWhereInput = {};
    
    if (criteria.transactionId) {
      where.transactionId = criteria.transactionId;
    }
    
    if (criteria.role && criteria.role.length > 0) {
      where.role = { in: criteria.role };
    }
    
    if (criteria.company) {
      where.company = {
        contains: criteria.company,
        mode: 'insensitive',
      };
    }
    
    if (criteria.search) {
      where.OR = [
        {
          firstName: {
            contains: criteria.search,
            mode: 'insensitive',
          },
        },
        {
          lastName: {
            contains: criteria.search,
            mode: 'insensitive',
          },
        },
        {
          email: {
            contains: criteria.search,
            mode: 'insensitive',
          },
        },
        {
          phone: {
            contains: criteria.search,
            mode: 'insensitive',
          },
        },
        {
          company: {
            contains: criteria.search,
            mode: 'insensitive',
          },
        },
      ];
    }

    // Build order by clause
    const orderBy: Prisma.ContactOrderByWithRelationInput = {};
    if (criteria.sortBy) {
      switch (criteria.sortBy) {
        case 'firstName':
          orderBy.firstName = criteria.sortOrder || 'asc';
          break;
        case 'lastName':
          orderBy.lastName = criteria.sortOrder || 'asc';
          break;
        case 'role':
          orderBy.role = criteria.sortOrder || 'asc';
          break;
        case 'company':
          orderBy.company = criteria.sortOrder || 'asc';
          break;
        case 'createdAt':
        default:
          orderBy.createdAt = criteria.sortOrder || 'desc';
          break;
      }
    } else {
      // Default sort: by last name, then first name
      orderBy.lastName = 'asc';
    }

    // Get total count and contacts
    const [total, contacts] = await Promise.all([
      prisma.contact.count({ where }),
      prisma.contact.findMany({
        where,
        skip,
        take: limit,
        orderBy: [orderBy, { firstName: 'asc' }],
        include: {
          transaction: {
            select: {
              id: true,
              propertyAddress: true,
            },
          },
        },
      }),
    ]);

    return {
      contacts,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Get contact statistics
   */
  async getStats(brokerageId?: string) {
    // Build base where clause
    const baseWhere: Prisma.ContactWhereInput = {};
    
    if (brokerageId) {
      baseWhere.transaction = { brokerageId };
    }

    const [
      total,
      roleCounts,
      companyCounts,
      withEmail,
      withPhone,
      recentlyAdded,
    ] = await Promise.all([
      prisma.contact.count({ where: baseWhere }),
      
      prisma.contact.groupBy({
        by: ['role'],
        where: baseWhere,
        _count: {
          role: true,
        },
      }),
      
      prisma.contact.groupBy({
        by: ['company'],
        where: {
          ...baseWhere,
          company: { not: null },
        },
        _count: {
          company: true,
        },
      }),
      
      prisma.contact.count({
        where: {
          ...baseWhere,
          email: { not: null },
        },
      }),
      
      prisma.contact.count({
        where: {
          ...baseWhere,
          phone: { not: null },
        },
      }),
      
      prisma.contact.count({
        where: {
          ...baseWhere,
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
          },
        },
      }),
    ]);

    // Convert role counts to object
    const byRole = roleCounts.reduce((acc, item) => {
      acc[item.role] = item._count.role;
      return acc;
    }, {} as Record<string, number>);

    // Convert company counts to object
    const byCompany = companyCounts.reduce((acc, item) => {
      if (item.company) {
        acc[item.company] = item._count.company;
      }
      return acc;
    }, {} as Record<string, number>);

    return {
      total,
      byRole,
      byCompany,
      withEmail,
      withPhone,
      recentlyAdded,
    };
  }

  /**
   * Get contacts by transaction
   */
  async findByTransaction(transactionId: string) {
    return prisma.contact.findMany({
      where: { transactionId },
      orderBy: [
        { role: 'asc' },
        { lastName: 'asc' },
        { firstName: 'asc' },
      ],
    });
  }

  /**
   * Get contacts by role
   */
  async findByRole(role: string, brokerageId?: string) {
    const where: Prisma.ContactWhereInput = { role };
    
    if (brokerageId) {
      where.transaction = { brokerageId };
    }

    return prisma.contact.findMany({
      where,
      orderBy: [
        { lastName: 'asc' },
        { firstName: 'asc' },
      ],
      include: {
        transaction: {
          select: {
            id: true,
            propertyAddress: true,
          },
        },
      },
    });
  }

  /**
   * Bulk update contacts
   */
  async bulkUpdate(operation: BulkContactOperation) {
    const { contactIds, operation: op, data } = operation;
    
    switch (op) {
      case 'update_role':
        return prisma.contact.updateMany({
          where: { id: { in: contactIds } },
          data: { role: data?.role },
        });
        
      case 'update_company':
        return prisma.contact.updateMany({
          where: { id: { in: contactIds } },
          data: { company: data?.company },
        });
        
      case 'delete':
        return prisma.contact.deleteMany({
          where: { id: { in: contactIds } },
        });
        
      default:
        throw new Error(`Unsupported bulk operation: ${op}`);
    }
  }

  /**
   * Get contact roles (distinct values)
   */
  async getRoles() {
    const result = await prisma.contact.findMany({
      select: {
        role: true,
      },
      distinct: ['role'],
      orderBy: {
        role: 'asc',
      },
    });

    return result.map(item => item.role);
  }

  /**
   * Get contact companies (distinct values)
   */
  async getCompanies() {
    const result = await prisma.contact.findMany({
      select: {
        company: true,
      },
      distinct: ['company'],
      where: {
        company: {
          not: null,
        },
      },
      orderBy: {
        company: 'asc',
      },
    });

    return result
      .map(item => item.company)
      .filter(company => company !== null) as string[];
  }

  /**
   * Check if contact exists by email in transaction
   */
  async existsByEmailInTransaction(email: string, transactionId: string, excludeId?: string) {
    const where: Prisma.ContactWhereInput = {
      email,
      transactionId,
    };
    
    if (excludeId) {
      where.id = { not: excludeId };
    }

    const contact = await prisma.contact.findFirst({ where });
    return !!contact;
  }

  /**
   * Get recent contacts
   */
  async findRecent(limit = 10, brokerageId?: string) {
    const where: Prisma.ContactWhereInput = {};
    
    if (brokerageId) {
      where.transaction = { brokerageId };
    }

    return prisma.contact.findMany({
      where,
      take: limit,
      orderBy: { createdAt: 'desc' },
      include: {
        transaction: {
          select: {
            id: true,
            propertyAddress: true,
          },
        },
      },
    });
  }
}

// Export singleton instance
export const contactRepository = new ContactRepository();
