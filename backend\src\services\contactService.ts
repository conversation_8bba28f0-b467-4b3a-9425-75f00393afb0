/**
 * Contact service
 * 
 * Business logic for contact management operations
 */

import { UserRole } from '@prisma/client';
import { contactRepository } from '../repositories/contactRepository';
import { transactionRepository } from '../repositories/transactionRepository';
import { AppError } from '../middleware/errorHandler';
import { 
  ContactDetails, 
  ContactSummary,
  ContactSearchCriteria, 
  CreateContactData,
  UpdateContactData,
  ContactListResponse,
  ContactStats,
  BulkContactOperation,
  ContactValidation
} from '../models/Contact';
import { logBusiness } from '../utils/logger';

/**
 * Contact service class
 */
export class ContactService {
  /**
   * Format contact data to details format
   */
  private formatContactDetails(contact: any): ContactDetails {
    const fullName = ContactValidation.getFullName(contact.firstName, contact.lastName);
    const displayName = ContactValidation.getDisplayName(contact.firstName, contact.lastName, contact.company);
    
    return {
      id: contact.id,
      transactionId: contact.transactionId,
      firstName: contact.firstName,
      lastName: contact.lastName,
      email: contact.email || undefined,
      phone: contact.phone || undefined,
      role: contact.role,
      company: contact.company || undefined,
      notes: contact.notes || undefined,
      createdAt: contact.createdAt,
      updatedAt: contact.updatedAt,
      transaction: contact.transaction,
      fullName,
      displayName,
    };
  }

  /**
   * Format contact data to summary format
   */
  private formatContactSummary(contact: any): ContactSummary {
    const fullName = ContactValidation.getFullName(contact.firstName, contact.lastName);
    const displayName = ContactValidation.getDisplayName(contact.firstName, contact.lastName, contact.company);
    
    return {
      id: contact.id,
      transactionId: contact.transactionId,
      firstName: contact.firstName,
      lastName: contact.lastName,
      email: contact.email || undefined,
      phone: contact.phone || undefined,
      role: contact.role,
      company: contact.company || undefined,
      createdAt: contact.createdAt,
      transaction: contact.transaction,
      fullName,
      displayName,
    };
  }

  /**
   * Create new contact
   */
  async createContact(
    contactData: CreateContactData, 
    createdBy: string,
    userBrokerageId: string
  ): Promise<ContactDetails> {
    // Validate transaction exists and user has access
    const transaction = await transactionRepository.findById(contactData.transactionId, false);
    if (!transaction) {
      throw new AppError('Transaction not found', 404, 'TRANSACTION_NOT_FOUND');
    }

    if (transaction.brokerageId !== userBrokerageId) {
      throw new AppError('Access denied to transaction', 403, 'ACCESS_DENIED');
    }

    // Validate contact data
    this.validateContactData(contactData);

    // Check for duplicate email in the same transaction
    if (contactData.email) {
      const emailExists = await contactRepository.existsByEmailInTransaction(
        contactData.email, 
        contactData.transactionId
      );
      if (emailExists) {
        throw new AppError('Contact with this email already exists in transaction', 409, 'EMAIL_EXISTS');
      }
    }

    const contact = await contactRepository.create(contactData);

    // Log the creation
    logBusiness.transactionCreated(contact.id, createdBy, `Contact created: ${contact.firstName} ${contact.lastName}`);

    return this.formatContactDetails(contact);
  }

  /**
   * Get contact by ID
   */
  async getContactById(id: string, userBrokerageId?: string): Promise<ContactDetails> {
    const contact = await contactRepository.findById(id, true);
    
    if (!contact) {
      throw new AppError('Contact not found', 404, 'CONTACT_NOT_FOUND');
    }

    // Check access for non-admin users
    if (userBrokerageId && contact.transaction.brokerageId !== userBrokerageId) {
      throw new AppError('Access denied', 403, 'ACCESS_DENIED');
    }

    return this.formatContactDetails(contact);
  }

  /**
   * Update contact
   */
  async updateContact(
    id: string, 
    updateData: UpdateContactData, 
    updatedBy: string,
    userBrokerageId: string
  ): Promise<ContactDetails> {
    // Check if contact exists
    const existingContact = await contactRepository.findById(id, true);
    if (!existingContact) {
      throw new AppError('Contact not found', 404, 'CONTACT_NOT_FOUND');
    }

    // Check access to transaction
    if (existingContact.transaction.brokerageId !== userBrokerageId) {
      throw new AppError('Access denied', 403, 'ACCESS_DENIED');
    }

    // Validate update data
    this.validateContactData(updateData, true);

    // Check for duplicate email if email is being changed
    if (updateData.email && updateData.email !== existingContact.email) {
      const emailExists = await contactRepository.existsByEmailInTransaction(
        updateData.email, 
        existingContact.transactionId,
        id
      );
      if (emailExists) {
        throw new AppError('Contact with this email already exists in transaction', 409, 'EMAIL_EXISTS');
      }
    }

    const updatedContact = await contactRepository.update(id, updateData);

    // Log the update
    logBusiness.transactionCreated(id, updatedBy, `Contact updated: ${updatedContact.firstName} ${updatedContact.lastName}`);

    return this.formatContactDetails(updatedContact);
  }

  /**
   * Delete contact
   */
  async deleteContact(id: string, deletedBy: string, userBrokerageId: string): Promise<void> {
    const contact = await contactRepository.findById(id, true);
    if (!contact) {
      throw new AppError('Contact not found', 404, 'CONTACT_NOT_FOUND');
    }

    // Check access
    if (contact.transaction.brokerageId !== userBrokerageId) {
      throw new AppError('Access denied', 403, 'ACCESS_DENIED');
    }

    await contactRepository.delete(id);

    // Log the deletion
    logBusiness.transactionCreated(id, deletedBy, `Contact deleted: ${contact.firstName} ${contact.lastName}`);
  }

  /**
   * Get contacts with search and pagination
   */
  async getContacts(
    criteria: ContactSearchCriteria,
    page = 1,
    limit = 10,
    requestingUser: { role: UserRole; brokerageId?: string; id: string }
  ): Promise<ContactListResponse> {
    // Apply role-based filtering
    const searchCriteria = { ...criteria };
    
    // Non-admin users can only see contacts from their brokerage
    // This will be handled by joining with transactions and filtering by brokerage
    
    const result = await contactRepository.findMany(searchCriteria, page, limit);

    // Filter by brokerage for non-admin users
    let filteredContacts = result.contacts;
    if (requestingUser.role !== UserRole.ADMIN && requestingUser.brokerageId) {
      filteredContacts = result.contacts.filter(
        contact => contact.transaction.brokerageId === requestingUser.brokerageId
      );
    }

    return {
      contacts: filteredContacts.map(contact => this.formatContactSummary(contact)),
      total: result.total,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages,
    };
  }

  /**
   * Get contact statistics
   */
  async getContactStats(requestingUser: { role: UserRole; brokerageId?: string; id: string }): Promise<ContactStats> {
    // Apply role-based filtering
    let brokerageId: string | undefined;
    
    if (requestingUser.role !== UserRole.ADMIN && requestingUser.brokerageId) {
      brokerageId = requestingUser.brokerageId;
    }

    return contactRepository.getStats(brokerageId);
  }

  /**
   * Get contacts by transaction
   */
  async getContactsByTransaction(
    transactionId: string,
    requestingUser: { role: UserRole; brokerageId?: string; id: string }
  ) {
    // Validate access to transaction
    const transaction = await transactionRepository.findById(transactionId, false);
    if (!transaction) {
      throw new AppError('Transaction not found', 404, 'TRANSACTION_NOT_FOUND');
    }

    // Check access based on role
    if (requestingUser.role !== UserRole.ADMIN && transaction.brokerageId !== requestingUser.brokerageId) {
      throw new AppError('Access denied', 403, 'ACCESS_DENIED');
    }

    const contacts = await contactRepository.findByTransaction(transactionId);
    return contacts.map(contact => this.formatContactSummary(contact));
  }

  /**
   * Get contacts by role
   */
  async getContactsByRole(
    role: string,
    requestingUser: { role: UserRole; brokerageId?: string; id: string }
  ) {
    const brokerageId = requestingUser.role !== UserRole.ADMIN ? requestingUser.brokerageId : undefined;
    const contacts = await contactRepository.findByRole(role, brokerageId);
    return contacts.map(contact => this.formatContactSummary(contact));
  }

  /**
   * Bulk update contacts
   */
  async bulkUpdateContacts(
    operation: BulkContactOperation,
    updatedBy: string,
    userBrokerageId: string
  ): Promise<void> {
    // Validate all contacts exist and user has access
    for (const contactId of operation.contactIds) {
      const contact = await contactRepository.findById(contactId, true);
      if (!contact) {
        throw new AppError(`Contact ${contactId} not found`, 404, 'CONTACT_NOT_FOUND');
      }

      if (contact.transaction.brokerageId !== userBrokerageId) {
        throw new AppError('Access denied to one or more contacts', 403, 'ACCESS_DENIED');
      }
    }

    await contactRepository.bulkUpdate(operation);

    // Log the bulk operation
    logBusiness.transactionCreated(
      operation.contactIds.join(','),
      updatedBy,
      `Bulk ${operation.operation} operation`
    );
  }

  /**
   * Validate contact data
   */
  private validateContactData(data: CreateContactData | UpdateContactData, isUpdate = false): void {
    // Validate email format if provided
    if (data.email && !ContactValidation.isValidEmail(data.email)) {
      throw new AppError('Invalid email format', 400, 'INVALID_EMAIL');
    }

    // Validate phone format if provided
    if (data.phone && !ContactValidation.isValidPhone(data.phone)) {
      throw new AppError('Invalid phone format', 400, 'INVALID_PHONE');
    }

    // Validate role if provided
    if (data.role && !ContactValidation.isValidRole(data.role)) {
      throw new AppError('Invalid contact role', 400, 'INVALID_ROLE');
    }

    // For create operations, ensure required fields are present
    if (!isUpdate) {
      const createData = data as CreateContactData;
      if (!createData.firstName?.trim()) {
        throw new AppError('First name is required', 400, 'MISSING_FIRST_NAME');
      }
      if (!createData.lastName?.trim()) {
        throw new AppError('Last name is required', 400, 'MISSING_LAST_NAME');
      }
      if (!createData.role?.trim()) {
        throw new AppError('Role is required', 400, 'MISSING_ROLE');
      }
    }
  }

  /**
   * Check if user can access contact
   */
  canAccessContact(
    requestingUser: { role: UserRole; brokerageId?: string; id: string },
    contact: { transaction: { brokerageId: string } }
  ): boolean {
    // Admins can access any contact
    if (requestingUser.role === UserRole.ADMIN) {
      return true;
    }

    // Users can only access contacts from their brokerage
    return requestingUser.brokerageId === contact.transaction.brokerageId;
  }

  /**
   * Validate contact permissions for an action
   */
  validateContactPermissions(
    requestingUser: { role: UserRole; brokerageId?: string; id: string },
    action: 'view' | 'edit' | 'delete',
    contact?: { transaction: { brokerageId: string } }
  ): void {
    if (!contact) {
      return; // Will be checked when contact is loaded
    }

    // Check basic access
    if (!this.canAccessContact(requestingUser, contact)) {
      throw new AppError('Access denied', 403, 'ACCESS_DENIED');
    }

    // Additional checks for edit/delete actions
    if (action === 'edit' || action === 'delete') {
      // Only TCs, Brokers, and Admins can edit/delete contacts
      if (![UserRole.TC, UserRole.BROKER, UserRole.ADMIN].includes(requestingUser.role)) {
        throw new AppError('Insufficient permissions', 403, 'INSUFFICIENT_PERMISSIONS');
      }
    }
  }
}

// Export singleton instance
export const contactService = new ContactService();
