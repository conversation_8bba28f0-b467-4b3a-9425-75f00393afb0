/**
 * ContactModal component
 * 
 * Modal for creating, editing, and viewing contacts
 */

'use client';

import { useState } from 'react';
import { ContactModalProps, CreateContactData, UpdateContactData, ContactFormErrors } from '@/types/contact';
import { ContactForm } from './ContactForm';
import { Modal } from '@/components/ui/Modal';

export function ContactModal({
  isOpen,
  onClose,
  contact,
  transactionId,
  mode,
  onSave,
}: ContactModalProps) {
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<ContactFormErrors>({});

  const handleSubmit = async (data: CreateContactData | UpdateContactData) => {
    setLoading(true);
    setErrors({});

    try {
      // TODO: Replace with actual API call
      if (mode === 'create') {
        // const newContact = await contactApi.createContact(data as CreateContactData);
        console.log('Creating contact:', data);
        
        // Mock successful creation
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        if (onSave) {
          // Mock contact response
          const mockContact = {
            id: Date.now().toString(),
            ...data,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            transaction: {
              id: transactionId || '',
              propertyAddress: '123 Main St, Anytown, PA 12345',
              status: 'active',
            },
            fullName: `${(data as CreateContactData).firstName} ${(data as CreateContactData).lastName}`,
            displayName: `${(data as CreateContactData).firstName} ${(data as CreateContactData).lastName}${(data as CreateContactData).company ? ` (${(data as CreateContactData).company})` : ''}`,
          };
          onSave(mockContact as any);
        }
      } else if (mode === 'edit' && contact) {
        // const updatedContact = await contactApi.updateContact(contact.id, data as UpdateContactData);
        console.log('Updating contact:', contact.id, data);
        
        // Mock successful update
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        if (onSave) {
          const updatedContact = {
            ...contact,
            ...data,
            updatedAt: new Date().toISOString(),
            fullName: `${data.firstName || contact.firstName} ${data.lastName || contact.lastName}`,
            displayName: `${data.firstName || contact.firstName} ${data.lastName || contact.lastName}${data.company || contact.company ? ` (${data.company || contact.company})` : ''}`,
          };
          onSave(updatedContact);
        }
      }

      onClose();
    } catch (error: any) {
      console.error('Error saving contact:', error);
      
      // Handle validation errors
      if (error.response?.status === 400 && error.response?.data?.errors) {
        setErrors(error.response.data.errors);
      } else {
        setErrors({
          general: error.message || 'An error occurred while saving the contact',
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const getModalTitle = () => {
    switch (mode) {
      case 'create':
        return 'Add New Contact';
      case 'edit':
        return 'Edit Contact';
      case 'view':
        return 'Contact Details';
      default:
        return 'Contact';
    }
  };

  const getModalSize = () => {
    return mode === 'view' ? 'lg' : 'xl';
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={getModalTitle()}
      size={getModalSize()}
    >
      {mode === 'view' && contact ? (
        // View Mode - Display contact details
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {contact.fullName}
              </h3>
              
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-500">Role</label>
                  <p className="mt-1 text-sm text-gray-900">{contact.role}</p>
                </div>
                
                {contact.company && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Company</label>
                    <p className="mt-1 text-sm text-gray-900">{contact.company}</p>
                  </div>
                )}
                
                {contact.email && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Email</label>
                    <p className="mt-1 text-sm text-gray-900">
                      <a href={`mailto:${contact.email}`} className="text-blue-600 hover:text-blue-700">
                        {contact.email}
                      </a>
                    </p>
                  </div>
                )}
                
                {contact.phone && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Phone</label>
                    <p className="mt-1 text-sm text-gray-900">
                      <a href={`tel:${contact.phone}`} className="text-blue-600 hover:text-blue-700">
                        {contact.phone}
                      </a>
                    </p>
                  </div>
                )}
              </div>
            </div>
            
            <div>
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-500">Transaction</label>
                  <p className="mt-1 text-sm text-gray-900">{contact.transaction.propertyAddress}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-500">Added</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {new Date(contact.createdAt).toLocaleDateString()}
                  </p>
                </div>
                
                {contact.updatedAt !== contact.createdAt && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Last Updated</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {new Date(contact.updatedAt).toLocaleDateString()}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
          
          {contact.notes && (
            <div>
              <label className="block text-sm font-medium text-gray-500 mb-2">Notes</label>
              <div className="bg-gray-50 rounded-md p-3">
                <p className="text-sm text-gray-900 whitespace-pre-wrap">{contact.notes}</p>
              </div>
            </div>
          )}
          
          <div className="flex justify-end pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Close
            </button>
          </div>
        </div>
      ) : (
        // Create/Edit Mode - Show form
        <ContactForm
          contact={contact}
          transactionId={transactionId}
          onSubmit={handleSubmit}
          onCancel={onClose}
          loading={loading}
          errors={errors}
        />
      )}
    </Modal>
  );
}
