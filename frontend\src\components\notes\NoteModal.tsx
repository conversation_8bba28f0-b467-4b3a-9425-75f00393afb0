/**
 * NoteModal component
 * 
 * Modal for creating, editing, and viewing notes
 */

'use client';

import { useState } from 'react';
import { NoteModalProps, CreateNoteData, UpdateNoteData, NoteFormErrors, NoteValidation } from '@/types/note';
import { NoteForm } from './NoteForm';
import { Modal } from '@/components/ui/Modal';

export function NoteModal({
  isOpen,
  onClose,
  note,
  transactionId,
  mode,
  onSave,
}: NoteModalProps) {
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<NoteFormErrors>({});

  // Mock available users for mentions
  const availableUsers = [
    {
      id: 'user-1',
      firstName: 'Jane',
      lastName: 'Doe',
      email: '<EMAIL>',
    },
    {
      id: 'user-2',
      firstName: 'John',
      lastName: 'Smith',
      email: '<EMAIL>',
    },
    {
      id: 'user-3',
      firstName: 'Bob',
      lastName: '<PERSON>',
      email: '<EMAIL>',
    },
  ];

  const handleSubmit = async (data: CreateNoteData | UpdateNoteData) => {
    setLoading(true);
    setErrors({});

    try {
      // TODO: Replace with actual API call
      if (mode === 'create') {
        // const newNote = await noteApi.createNote(data as CreateNoteData);
        console.log('Creating note:', data);
        
        // Mock successful creation
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        if (onSave) {
          // Mock note response
          const mockNote = {
            id: Date.now().toString(),
            ...data,
            userId: 'user-1', // Current user
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            transaction: {
              id: transactionId || '',
              propertyAddress: '123 Main St, Anytown, PA 12345',
              status: 'active',
            },
            user: {
              id: 'user-1',
              firstName: 'Current',
              lastName: 'User',
              email: '<EMAIL>',
            },
            mentionedUsers: availableUsers.filter(user => 
              (data as CreateNoteData).mentions?.includes(user.id)
            ),
            isEdited: false,
            canEdit: true,
            canDelete: true,
            timeAgo: 'just now',
            mentionCount: (data as CreateNoteData).mentions?.length || 0,
          };
          onSave(mockNote as any);
        }
      } else if (mode === 'edit' && note) {
        // const updatedNote = await noteApi.updateNote(note.id, data as UpdateNoteData);
        console.log('Updating note:', note.id, data);
        
        // Mock successful update
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        if (onSave) {
          const updatedNote = {
            ...note,
            ...data,
            updatedAt: new Date().toISOString(),
            isEdited: true,
            mentionedUsers: availableUsers.filter(user => 
              (data as UpdateNoteData).mentions?.includes(user.id)
            ),
            mentionCount: (data as UpdateNoteData).mentions?.length || 0,
          };
          onSave(updatedNote);
        }
      }

      onClose();
    } catch (error: any) {
      console.error('Error saving note:', error);
      
      // Handle validation errors
      if (error.response?.status === 400 && error.response?.data?.errors) {
        setErrors(error.response.data.errors);
      } else {
        setErrors({
          general: error.message || 'An error occurred while saving the note',
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const getModalTitle = () => {
    switch (mode) {
      case 'create':
        return 'Add New Note';
      case 'edit':
        return 'Edit Note';
      case 'view':
        return 'Note Details';
      default:
        return 'Note';
    }
  };

  const getModalSize = () => {
    return mode === 'view' ? 'lg' : 'xl';
  };

  const formatContentWithMentions = (content: string) => {
    if (!note?.mentionedUsers) return content;
    
    return NoteValidation.formatContentWithMentions(content, note.mentionedUsers.map(user => ({
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      displayName: `${user.firstName} ${user.lastName}`,
    })));
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={getModalTitle()}
      size={getModalSize()}
    >
      {mode === 'view' && note ? (
        // View Mode - Display note details
        <div className="space-y-6">
          {/* Note Header */}
          <div className="flex items-start space-x-4">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white font-medium">
                  {note.user.firstName.charAt(0)}{note.user.lastName.charAt(0)}
                </span>
              </div>
            </div>
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <h3 className="text-lg font-semibold text-gray-900">
                  {note.user.firstName} {note.user.lastName}
                </h3>
                <span className="text-sm text-gray-500">
                  {note.timeAgo}
                </span>
                {note.isEdited && (
                  <span className="text-xs text-gray-400">(edited)</span>
                )}
              </div>
              <p className="text-sm text-gray-600">{note.user.email}</p>
            </div>
          </div>

          {/* Note Content */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="text-gray-900 whitespace-pre-wrap break-words">
              {formatContentWithMentions(note.content)}
            </div>
          </div>

          {/* Mentions */}
          {note.mentionedUsers && note.mentionedUsers.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Mentioned Users</h4>
              <div className="flex flex-wrap gap-2">
                {note.mentionedUsers.map(user => (
                  <div
                    key={user.id}
                    className="inline-flex items-center space-x-2 bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm"
                  >
                    <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">
                        {user.firstName.charAt(0)}{user.lastName.charAt(0)}
                      </span>
                    </div>
                    <span>{user.firstName} {user.lastName}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Transaction Info */}
          <div className="border-t border-gray-200 pt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <label className="block font-medium text-gray-500">Transaction</label>
                <p className="mt-1 text-gray-900">{note.transaction.propertyAddress}</p>
              </div>
              <div>
                <label className="block font-medium text-gray-500">Created</label>
                <p className="mt-1 text-gray-900">
                  {new Date(note.createdAt).toLocaleString()}
                </p>
              </div>
            </div>
          </div>
          
          <div className="flex justify-end pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Close
            </button>
          </div>
        </div>
      ) : (
        // Create/Edit Mode - Show form
        <NoteForm
          note={note}
          transactionId={transactionId}
          onSubmit={handleSubmit}
          onCancel={onClose}
          loading={loading}
          errors={errors}
          placeholder={mode === 'create' ? 'Write a note...' : 'Edit your note...'}
          showMentions={true}
          availableUsers={availableUsers}
        />
      )}
    </Modal>
  );
}
