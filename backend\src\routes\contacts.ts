/**
 * Contact management routes
 *
 * <PERSON>les contact CRUD operations for transactions
 */

import express from 'express';
import { UserRole } from '@prisma/client';
import { asyncHandler, AppError } from '../middleware/errorHandler';
import { authenticate, authorize, AuthenticatedRequest } from '../middleware/auth';
import { contactService } from '../services/contactService';
import { CreateContactData, UpdateContactData, ContactSearchCriteria, BulkContactOperation, ContactRoles } from '../models/Contact';
import { ValidationHelpers, ValidationPatterns } from '../utils/validation';
import Joi from 'joi';

const router = express.Router();

/**
 * Validation schemas
 */
const createContactSchema = Joi.object({
  transactionId: ValidationPatterns.uuid.required(),
  firstName: Joi.string().min(1).max(50).required(),
  lastName: Joi.string().min(1).max(50).required(),
  email: Joi.string().email().max(100).optional().allow(''),
  phone: Joi.string().max(20).optional().allow(''),
  role: Joi.string().valid(...Object.values(ContactRoles)).required(),
  company: Joi.string().max(100).optional().allow(''),
  notes: Joi.string().max(500).optional().allow(''),
});

const updateContactSchema = Joi.object({
  firstName: Joi.string().min(1).max(50).optional(),
  lastName: Joi.string().min(1).max(50).optional(),
  email: Joi.string().email().max(100).optional().allow(''),
  phone: Joi.string().max(20).optional().allow(''),
  role: Joi.string().valid(...Object.values(ContactRoles)).optional(),
  company: Joi.string().max(100).optional().allow(''),
  notes: Joi.string().max(500).optional().allow(''),
});

const contactSearchSchema = Joi.object({
  transactionId: ValidationPatterns.uuid.optional(),
  role: Joi.array().items(Joi.string()).optional(),
  company: Joi.string().max(100).optional(),
  search: ValidationPatterns.search.optional(),
  sortBy: Joi.string().valid('firstName', 'lastName', 'role', 'company', 'createdAt').optional(),
  sortOrder: Joi.string().valid('asc', 'desc').optional(),
  page: ValidationPatterns.page,
  limit: ValidationPatterns.limit,
});

const bulkContactSchema = Joi.object({
  contactIds: Joi.array().items(ValidationPatterns.uuid).min(1).required(),
  operation: Joi.string().valid('delete', 'update_role', 'update_company', 'export').required(),
  data: Joi.object({
    role: Joi.string().valid(...Object.values(ContactRoles)).optional(),
    company: Joi.string().max(100).optional(),
  }).optional(),
});

/**
 * All contact routes require authentication
 */
router.use(authenticate);

/**
 * GET /api/contacts
 * Get contacts with filtering and pagination
 */
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  // Validate query parameters
  const { page, limit, ...searchCriteria } = ValidationHelpers.validateQuery(contactSearchSchema, req.query);

  const result = await contactService.getContacts(
    searchCriteria as ContactSearchCriteria,
    page,
    limit,
    req.user!
  );

  res.json({
    message: 'Contacts retrieved successfully',
    data: result,
  });
}));

/**
 * GET /api/contacts/stats
 * Get contact statistics
 */
router.get('/stats', authorize(UserRole.ADMIN, UserRole.TC, UserRole.BROKER), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const stats = await contactService.getContactStats(req.user!);

  res.json({
    message: 'Contact statistics retrieved successfully',
    data: stats,
  });
}));

/**
 * GET /api/contacts/roles
 * Get available contact roles
 */
router.get('/roles', asyncHandler(async (req: AuthenticatedRequest, res) => {
  res.json({
    message: 'Contact roles retrieved successfully',
    data: Object.values(ContactRoles),
  });
}));

/**
 * POST /api/contacts
 * Create new contact
 */
router.post('/', authorize(UserRole.TC, UserRole.ADMIN, UserRole.BROKER), asyncHandler(async (req: AuthenticatedRequest, res) => {
  // Validate request body
  const contactData: CreateContactData = ValidationHelpers.validateBody(createContactSchema, req.body);

  const contact = await contactService.createContact(
    contactData,
    req.user!.id,
    req.user!.brokerageId!
  );

  res.status(201).json({
    message: 'Contact created successfully',
    data: contact,
  });
}));

/**
 * POST /api/contacts/bulk
 * Bulk update contacts
 */
router.post('/bulk', authorize(UserRole.TC, UserRole.ADMIN, UserRole.BROKER), asyncHandler(async (req: AuthenticatedRequest, res) => {
  // Validate request body
  const operation: BulkContactOperation = ValidationHelpers.validateBody(bulkContactSchema, req.body);

  await contactService.bulkUpdateContacts(
    operation,
    req.user!.id,
    req.user!.brokerageId!
  );

  res.json({
    message: 'Bulk contact operation completed successfully',
  });
}));

/**
 * GET /api/contacts/:id
 * Get contact details
 */
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  // Get contact with permission check
  const contact = await contactService.getContactById(id, req.user!.brokerageId);

  res.json({
    message: 'Contact retrieved successfully',
    data: contact,
  });
}));

/**
 * PUT /api/contacts/:id
 * Update contact
 */
router.put('/:id', authorize(UserRole.TC, UserRole.ADMIN, UserRole.BROKER), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  // Validate request body
  const updateData: UpdateContactData = ValidationHelpers.validateBody(updateContactSchema, req.body);

  // Update contact
  const updatedContact = await contactService.updateContact(
    id,
    updateData,
    req.user!.id,
    req.user!.brokerageId!
  );

  res.json({
    message: 'Contact updated successfully',
    data: updatedContact,
  });
}));

/**
 * DELETE /api/contacts/:id
 * Delete contact
 */
router.delete('/:id', authorize(UserRole.TC, UserRole.ADMIN, UserRole.BROKER), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  await contactService.deleteContact(id, req.user!.id, req.user!.brokerageId!);

  res.json({
    message: 'Contact deleted successfully',
  });
}));

/**
 * GET /api/contacts/transaction/:transactionId
 * Get contacts by transaction
 */
router.get('/transaction/:transactionId', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { transactionId } = req.params;

  const contacts = await contactService.getContactsByTransaction(transactionId, req.user!);

  res.json({
    message: 'Transaction contacts retrieved successfully',
    data: contacts,
  });
}));

/**
 * GET /api/contacts/role/:role
 * Get contacts by role
 */
router.get('/role/:role', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { role } = req.params;

  // Validate role
  if (!Object.values(ContactRoles).includes(role as any)) {
    throw new AppError('Invalid contact role', 400, 'INVALID_ROLE');
  }

  const contacts = await contactService.getContactsByRole(role, req.user!);

  res.json({
    message: 'Contacts by role retrieved successfully',
    data: contacts,
  });
}));

export default router;
