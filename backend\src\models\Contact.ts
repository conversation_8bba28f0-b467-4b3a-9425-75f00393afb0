/**
 * Contact model interfaces and types
 * 
 * Defines TypeScript interfaces for Contact-related operations
 */

/**
 * Contact creation data
 */
export interface CreateContactData {
  transactionId: string;
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  role: string; // buyer, seller, attorney, inspector, etc.
  company?: string;
  notes?: string;
}

/**
 * Contact update data
 */
export interface UpdateContactData {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  role?: string;
  company?: string;
  notes?: string;
}

/**
 * Contact details
 */
export interface ContactDetails {
  id: string;
  transactionId: string;
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  role: string;
  company?: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
  
  // Related entities
  transaction: {
    id: string;
    propertyAddress: string;
    status: string;
  };
  
  // Calculated fields
  fullName: string;
  displayName: string;
}

/**
 * Contact summary (for lists)
 */
export interface ContactSummary {
  id: string;
  transactionId: string;
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  role: string;
  company?: string;
  createdAt: Date;
  
  // Related entities
  transaction: {
    id: string;
    propertyAddress: string;
  };
  
  // Calculated fields
  fullName: string;
  displayName: string;
}

/**
 * Contact search criteria
 */
export interface ContactSearchCriteria {
  transactionId?: string;
  role?: string[];
  company?: string;
  search?: string; // Search in firstName, lastName, email, phone, company
  sortBy?: 'firstName' | 'lastName' | 'role' | 'company' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
}

/**
 * Contact list response with pagination
 */
export interface ContactListResponse {
  contacts: ContactSummary[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * Contact statistics
 */
export interface ContactStats {
  total: number;
  byRole: Record<string, number>;
  byCompany: Record<string, number>;
  withEmail: number;
  withPhone: number;
  recentlyAdded: number; // Added in last 30 days
}

/**
 * Contact role types (common real estate contact roles)
 */
export const ContactRoles = {
  BUYER: 'buyer',
  SELLER: 'seller',
  LISTING_AGENT: 'listing_agent',
  SELLING_AGENT: 'selling_agent',
  ATTORNEY: 'attorney',
  TITLE_COMPANY: 'title_company',
  LENDER: 'lender',
  INSPECTOR: 'inspector',
  APPRAISER: 'appraiser',
  CONTRACTOR: 'contractor',
  INSURANCE_AGENT: 'insurance_agent',
  ESCROW_OFFICER: 'escrow_officer',
  NOTARY: 'notary',
  OTHER: 'other',
} as const;

export type ContactRole = typeof ContactRoles[keyof typeof ContactRoles];

/**
 * Contact role display names
 */
export const ContactRoleDisplayNames: Record<ContactRole, string> = {
  [ContactRoles.BUYER]: 'Buyer',
  [ContactRoles.SELLER]: 'Seller',
  [ContactRoles.LISTING_AGENT]: 'Listing Agent',
  [ContactRoles.SELLING_AGENT]: 'Selling Agent',
  [ContactRoles.ATTORNEY]: 'Attorney',
  [ContactRoles.TITLE_COMPANY]: 'Title Company',
  [ContactRoles.LENDER]: 'Lender',
  [ContactRoles.INSPECTOR]: 'Inspector',
  [ContactRoles.APPRAISER]: 'Appraiser',
  [ContactRoles.CONTRACTOR]: 'Contractor',
  [ContactRoles.INSURANCE_AGENT]: 'Insurance Agent',
  [ContactRoles.ESCROW_OFFICER]: 'Escrow Officer',
  [ContactRoles.NOTARY]: 'Notary',
  [ContactRoles.OTHER]: 'Other',
};

/**
 * Contact validation helpers
 */
export const ContactValidation = {
  /**
   * Validate email format
   */
  isValidEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  /**
   * Validate phone format (basic US phone number validation)
   */
  isValidPhone: (phone: string): boolean => {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)\.]/g, ''));
  },

  /**
   * Validate contact role
   */
  isValidRole: (role: string): boolean => {
    return Object.values(ContactRoles).includes(role as ContactRole);
  },

  /**
   * Format phone number for display
   */
  formatPhone: (phone: string): string => {
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    }
    return phone;
  },

  /**
   * Get full name from first and last name
   */
  getFullName: (firstName: string, lastName: string): string => {
    return `${firstName} ${lastName}`.trim();
  },

  /**
   * Get display name (full name with company if available)
   */
  getDisplayName: (firstName: string, lastName: string, company?: string): string => {
    const fullName = ContactValidation.getFullName(firstName, lastName);
    return company ? `${fullName} (${company})` : fullName;
  },
};

/**
 * Contact export data (for CSV/Excel export)
 */
export interface ContactExportData {
  transactionId: string;
  propertyAddress: string;
  firstName: string;
  lastName: string;
  fullName: string;
  email?: string;
  phone?: string;
  role: string;
  roleDisplayName: string;
  company?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Contact import data (for CSV/Excel import)
 */
export interface ContactImportData {
  transactionId: string;
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  role: string;
  company?: string;
  notes?: string;
}

/**
 * Bulk contact operation data
 */
export interface BulkContactOperation {
  contactIds: string[];
  operation: 'delete' | 'update_role' | 'update_company' | 'export';
  data?: {
    role?: string;
    company?: string;
  };
}
