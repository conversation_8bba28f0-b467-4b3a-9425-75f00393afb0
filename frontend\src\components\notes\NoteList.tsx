/**
 * NoteList component
 *
 * Displays a list of notes with search, filtering, and pagination
 */

'use client';

import { useState, useEffect } from 'react';
import { NoteListProps, NoteSummary, NoteSearchCriteria, NoteValidation } from '@/types/note';
import { NoteCard } from './NoteCard';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

// Icons
const SearchIcon = () => <span>🔍</span>;
const FilterIcon = () => <span>🔽</span>;
const ThreadIcon = () => <span>💬</span>;

interface NoteListState {
  notes: NoteSummary[];
  loading: boolean;
  error: string | null;
  total: number;
  page: number;
  totalPages: number;
}

export function NoteList({
  transactionId,
  searchCriteria = {},
  onNoteSelect,
  onNoteEdit,
  onNoteDelete,
  showActions = true,
  compact = false,
  showThread = false,
}: NoteListProps) {
  const [state, setState] = useState<NoteListState>({
    notes: [],
    loading: true,
    error: null,
    total: 0,
    page: 1,
    totalPages: 1,
  });

  const [criteria, setCriteria] = useState<NoteSearchCriteria>({
    ...searchCriteria,
    transactionId,
    page: 1,
    limit: 10,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });

  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  // Mock data for development
  const mockNotes: NoteSummary[] = [
    {
      id: '1',
      transactionId: transactionId || 'trans-1',
      userId: 'user-1',
      content: 'Just spoke with the buyer. They are excited about the property and want to schedule an inspection for next week. @[John Smith](user-2) can you coordinate with the inspector?',
      mentions: ['user-2'],
      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
      updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      transaction: {
        id: transactionId || 'trans-1',
        propertyAddress: '123 Main St, Anytown, PA 12345',
      },
      user: {
        id: 'user-1',
        firstName: 'Jane',
        lastName: 'Doe',
      },
      isEdited: false,
      timeAgo: NoteValidation.getTimeAgo(new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()),
      mentionCount: 1,
      contentPreview: NoteValidation.getContentPreview('Just spoke with the buyer. They are excited about the property and want to schedule an inspection for next week. @[John Smith](user-2) can you coordinate with the inspector?'),
    },
    {
      id: '2',
      transactionId: transactionId || 'trans-1',
      userId: 'user-2',
      content: 'Inspection scheduled for Thursday at 2 PM. Inspector will be Tom Wilson from ABC Inspections.',
      mentions: [],
      createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 minutes ago
      updatedAt: new Date(Date.now() - 25 * 60 * 1000).toISOString(), // edited 25 minutes ago
      transaction: {
        id: transactionId || 'trans-1',
        propertyAddress: '123 Main St, Anytown, PA 12345',
      },
      user: {
        id: 'user-2',
        firstName: 'John',
        lastName: 'Smith',
      },
      isEdited: true,
      timeAgo: NoteValidation.getTimeAgo(new Date(Date.now() - 30 * 60 * 1000).toISOString()),
      mentionCount: 0,
      contentPreview: 'Inspection scheduled for Thursday at 2 PM. Inspector will be Tom Wilson from ABC Inspections.',
    },
  ];

  useEffect(() => {
    loadNotes();
  }, [criteria]);

  const loadNotes = async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      // TODO: Replace with actual API call
      // const response = await noteApi.getNotes(criteria);

      // Mock API response
      await new Promise(resolve => setTimeout(resolve, 500));

      // Filter notes based on search criteria
      let filteredNotes = [...mockNotes];

      if (criteria.search) {
        const searchLower = criteria.search.toLowerCase();
        filteredNotes = filteredNotes.filter(note =>
          note.content.toLowerCase().includes(searchLower) ||
          note.user.firstName.toLowerCase().includes(searchLower) ||
          note.user.lastName.toLowerCase().includes(searchLower)
        );
      }

      // Add calculated fields to mock notes
      filteredNotes = filteredNotes.map(note => ({
        ...note,
        timeAgo: NoteValidation.getTimeAgo(note.createdAt),
        isEdited: NoteValidation.isEdited(note.createdAt, note.updatedAt),
        contentPreview: NoteValidation.getContentPreview(note.content),
      }));

      setState(prev => ({
        ...prev,
        notes: filteredNotes,
        total: filteredNotes.length,
        totalPages: Math.ceil(filteredNotes.length / (criteria.limit || 10)),
        loading: false,
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: 'Failed to load notes',
        loading: false,
      }));
    }
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setCriteria(prev => ({
      ...prev,
      search: term || undefined,
      page: 1,
    }));
  };

  const handleCriteriaChange = (newCriteria: NoteSearchCriteria) => {
    setCriteria(prev => ({
      ...prev,
      ...newCriteria,
      page: 1,
    }));
  };

  const handlePageChange = (page: number) => {
    setCriteria(prev => ({ ...prev, page }));
  };

  const handleNoteSelect = (note: NoteSummary) => {
    if (onNoteSelect) {
      onNoteSelect(note as any); // Type conversion for compatibility
    }
  };

  const handleNoteEdit = (note: NoteSummary) => {
    if (onNoteEdit) {
      onNoteEdit(note as any); // Type conversion for compatibility
    }
  };

  const handleNoteDelete = async (noteId: string) => {
    if (onNoteDelete) {
      await onNoteDelete(noteId);
      // Reload notes after deletion
      loadNotes();
    }
  };

  const handleNoteReply = (note: NoteSummary) => {
    // TODO: Implement reply functionality
    console.log('Reply to note:', note.id);
  };

  const resetFilters = () => {
    setSearchTerm('');
    setCriteria({
      transactionId,
      page: 1,
      limit: 10,
      sortBy: 'createdAt',
      sortOrder: 'desc',
    });
    setShowFilters(false);
  };

  if (state.loading && state.notes.length === 0) {
    return (
      <div className="flex items-center justify-center py-8">
        <LoadingSpinner size="lg" />
        <span className="ml-2 text-gray-600">Loading notes...</span>
      </div>
    );
  }

  if (state.error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <p className="text-red-600">{state.error}</p>
        <Button
          variant="outline"
          size="sm"
          onClick={loadNotes}
          className="mt-2"
        >
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <SearchIcon />
            </div>
            <Input
              type="text"
              placeholder="Search notes..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <div className="flex gap-2">
          {showThread && (
            <Button
              variant="outline"
              leftIcon={<ThreadIcon />}
            >
              Thread View
            </Button>
          )}
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            leftIcon={<FilterIcon />}
          >
            Filters
          </Button>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="bg-gray-50 rounded-lg p-4">
          <p className="text-sm text-gray-600">Note filters will be implemented here</p>
          {/* TODO: Implement note filters */}
        </div>
      )}

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-gray-600">
          {state.total === 0 ? 'No notes found' : `${state.total} note${state.total === 1 ? '' : 's'} found`}
        </p>

        {state.loading && (
          <LoadingSpinner size="sm" />
        )}
      </div>

      {/* Notes List */}
      {state.notes.length > 0 ? (
        <div className="space-y-4">
          {state.notes.map((note) => (
            <NoteCard
              key={note.id}
              note={note}
              onEdit={showActions ? handleNoteEdit : undefined}
              onDelete={showActions ? handleNoteDelete : undefined}
              onView={handleNoteSelect}
              onReply={showActions ? handleNoteReply : undefined}
              showActions={showActions}
              compact={compact}
              currentUserId="user-1" // TODO: Get from auth context
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <p className="text-gray-500 mb-4">No notes found</p>
          {transactionId && (
            <p className="text-sm text-gray-400">
              Add notes to start collaborating on this transaction
            </p>
          )}
        </div>
      )}

      {/* Pagination */}
      {state.totalPages > 1 && (
        <div className="flex items-center justify-center space-x-2 pt-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(state.page - 1)}
            disabled={state.page === 1 || state.loading}
          >
            Previous
          </Button>

          <span className="text-sm text-gray-600">
            Page {state.page} of {state.totalPages}
          </span>

          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(state.page + 1)}
            disabled={state.page === state.totalPages || state.loading}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );
}
