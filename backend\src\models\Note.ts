/**
 * Note model interfaces and types
 * 
 * Defines TypeScript interfaces for Note/Communication-related operations
 */

/**
 * Note creation data
 */
export interface CreateNoteData {
  transactionId: string;
  content: string;
  mentions?: string[]; // Array of user IDs mentioned in the note
}

/**
 * Note update data
 */
export interface UpdateNoteData {
  content?: string;
  mentions?: string[];
}

/**
 * Note details
 */
export interface NoteDetails {
  id: string;
  transactionId: string;
  userId: string;
  content: string;
  mentions: string[];
  createdAt: Date;
  updatedAt: Date;
  
  // Related entities
  transaction: {
    id: string;
    propertyAddress: string;
    status: string;
  };
  user: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  mentionedUsers?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  }[];
  
  // Calculated fields
  isEdited: boolean;
  canEdit: boolean;
  canDelete: boolean;
  timeAgo: string;
  mentionCount: number;
}

/**
 * Note summary (for lists)
 */
export interface NoteSummary {
  id: string;
  transactionId: string;
  userId: string;
  content: string;
  mentions: string[];
  createdAt: Date;
  updatedAt: Date;
  
  // Related entities
  transaction: {
    id: string;
    propertyAddress: string;
  };
  user: {
    id: string;
    firstName: string;
    lastName: string;
  };
  
  // Calculated fields
  isEdited: boolean;
  timeAgo: string;
  mentionCount: number;
  contentPreview: string; // Truncated content for lists
}

/**
 * Note search criteria
 */
export interface NoteSearchCriteria {
  transactionId?: string;
  userId?: string;
  mentionedUserId?: string; // Find notes where a specific user is mentioned
  search?: string; // Search in content
  dateFrom?: Date;
  dateTo?: Date;
  sortBy?: 'createdAt' | 'updatedAt' | 'content';
  sortOrder?: 'asc' | 'desc';
}

/**
 * Note list response with pagination
 */
export interface NoteListResponse {
  notes: NoteSummary[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * Note statistics
 */
export interface NoteStats {
  total: number;
  byUser: Record<string, number>;
  byTransaction: Record<string, number>;
  totalMentions: number;
  recentActivity: number; // Notes in last 7 days
  averageNotesPerTransaction: number;
  mostActiveUsers: {
    userId: string;
    userName: string;
    noteCount: number;
  }[];
}

/**
 * Note mention data
 */
export interface NoteMention {
  userId: string;
  userName: string;
  userEmail: string;
  noteId: string;
  noteContent: string;
  transactionId: string;
  propertyAddress: string;
  mentionedAt: Date;
  isRead: boolean;
}

/**
 * Note activity data (for activity feeds)
 */
export interface NoteActivity {
  id: string;
  type: 'note_created' | 'note_updated' | 'note_mentioned';
  noteId: string;
  transactionId: string;
  userId: string;
  content: string;
  propertyAddress: string;
  userName: string;
  createdAt: Date;
  mentions?: string[];
}

/**
 * Note validation helpers
 */
export const NoteValidation = {
  /**
   * Validate note content
   */
  isValidContent: (content: string): boolean => {
    return content.trim().length > 0 && content.length <= 2000;
  },

  /**
   * Extract mentions from content
   */
  extractMentions: (content: string): string[] => {
    const mentionRegex = /@\[([^\]]+)\]\(([^)]+)\)/g;
    const mentions: string[] = [];
    let match;
    
    while ((match = mentionRegex.exec(content)) !== null) {
      const userId = match[2];
      if (userId && !mentions.includes(userId)) {
        mentions.push(userId);
      }
    }
    
    return mentions;
  },

  /**
   * Format content with mentions for display
   */
  formatContentWithMentions: (content: string, users: { id: string; firstName: string; lastName: string }[]): string => {
    let formattedContent = content;
    
    users.forEach(user => {
      const mentionPattern = new RegExp(`@\\[([^\\]]+)\\]\\(${user.id}\\)`, 'g');
      formattedContent = formattedContent.replace(
        mentionPattern,
        `@${user.firstName} ${user.lastName}`
      );
    });
    
    return formattedContent;
  },

  /**
   * Create mention markup for content
   */
  createMentionMarkup: (userId: string, userName: string): string => {
    return `@[${userName}](${userId})`;
  },

  /**
   * Get content preview (truncated)
   */
  getContentPreview: (content: string, maxLength = 100): string => {
    if (content.length <= maxLength) {
      return content;
    }
    return content.substring(0, maxLength).trim() + '...';
  },

  /**
   * Calculate time ago string
   */
  getTimeAgo: (date: Date): string => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) {
      return 'just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 604800) {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    } else {
      return date.toLocaleDateString();
    }
  },

  /**
   * Check if note was edited
   */
  isEdited: (createdAt: Date, updatedAt: Date): boolean => {
    return updatedAt.getTime() - createdAt.getTime() > 1000; // More than 1 second difference
  },
};

/**
 * Note notification data
 */
export interface NoteNotification {
  id: string;
  type: 'mention' | 'reply' | 'note_added';
  noteId: string;
  transactionId: string;
  fromUserId: string;
  toUserId: string;
  content: string;
  propertyAddress: string;
  fromUserName: string;
  isRead: boolean;
  createdAt: Date;
}

/**
 * Bulk note operation data
 */
export interface BulkNoteOperation {
  noteIds: string[];
  operation: 'delete' | 'export';
}

/**
 * Note export data (for CSV/Excel export)
 */
export interface NoteExportData {
  id: string;
  transactionId: string;
  propertyAddress: string;
  content: string;
  authorName: string;
  authorEmail: string;
  mentions: string;
  createdAt: string;
  updatedAt: string;
  isEdited: boolean;
}

/**
 * Note thread data (for conversation view)
 */
export interface NoteThread {
  transactionId: string;
  propertyAddress: string;
  notes: NoteSummary[];
  participants: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    noteCount: number;
    lastActivity: Date;
  }[];
  totalNotes: number;
  lastActivity: Date;
}

/**
 * Note collaboration data
 */
export interface NoteCollaboration {
  transactionId: string;
  activeUsers: {
    id: string;
    firstName: string;
    lastName: string;
    lastSeen: Date;
    isTyping: boolean;
  }[];
  recentNotes: NoteSummary[];
  unreadMentions: number;
}
