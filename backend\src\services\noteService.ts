/**
 * Note service
 * 
 * Business logic for note management and communication operations
 */

import { UserRole } from '@prisma/client';
import { noteRepository } from '../repositories/noteRepository';
import { transactionRepository } from '../repositories/transactionRepository';
import { userRepository } from '../repositories/userRepository';
import { AppError } from '../middleware/errorHandler';
import { 
  NoteDetails, 
  NoteSummary,
  NoteSearchCriteria, 
  CreateNoteData,
  UpdateNoteData,
  NoteListResponse,
  NoteStats,
  BulkNoteOperation,
  NoteValidation,
  NoteThread,
  NoteMention
} from '../models/Note';
import { logBusiness } from '../utils/logger';

/**
 * Note service class
 */
export class NoteService {
  /**
   * Format note data to details format
   */
  private formatNoteDetails(note: any, requestingUserId?: string): NoteDetails {
    const isEdited = NoteValidation.isEdited(note.createdAt, note.updatedAt);
    const timeAgo = NoteValidation.getTimeAgo(note.createdAt);
    
    return {
      id: note.id,
      transactionId: note.transactionId,
      userId: note.userId,
      content: note.content,
      mentions: note.mentions || [],
      createdAt: note.createdAt,
      updatedAt: note.updatedAt,
      transaction: note.transaction,
      user: note.user,
      mentionedUsers: note.mentionedUsers || [],
      isEdited,
      canEdit: requestingUserId === note.userId,
      canDelete: requestingUserId === note.userId,
      timeAgo,
      mentionCount: note.mentions?.length || 0,
    };
  }

  /**
   * Format note data to summary format
   */
  private formatNoteSummary(note: any): NoteSummary {
    const isEdited = NoteValidation.isEdited(note.createdAt, note.updatedAt);
    const timeAgo = NoteValidation.getTimeAgo(note.createdAt);
    const contentPreview = NoteValidation.getContentPreview(note.content);
    
    return {
      id: note.id,
      transactionId: note.transactionId,
      userId: note.userId,
      content: note.content,
      mentions: note.mentions || [],
      createdAt: note.createdAt,
      updatedAt: note.updatedAt,
      transaction: note.transaction,
      user: note.user,
      isEdited,
      timeAgo,
      mentionCount: note.mentions?.length || 0,
      contentPreview,
    };
  }

  /**
   * Create new note
   */
  async createNote(
    noteData: CreateNoteData, 
    createdBy: string,
    userBrokerageId: string
  ): Promise<NoteDetails> {
    // Validate transaction exists and user has access
    const transaction = await transactionRepository.findById(noteData.transactionId, false);
    if (!transaction) {
      throw new AppError('Transaction not found', 404, 'TRANSACTION_NOT_FOUND');
    }

    if (transaction.brokerageId !== userBrokerageId) {
      throw new AppError('Access denied to transaction', 403, 'ACCESS_DENIED');
    }

    // Validate note content
    this.validateNoteData(noteData);

    // Extract mentions from content if not provided
    let mentions = noteData.mentions || [];
    if (!mentions.length) {
      mentions = NoteValidation.extractMentions(noteData.content);
    }

    // Validate mentioned users exist and have access to the transaction
    if (mentions.length > 0) {
      await this.validateMentionedUsers(mentions, userBrokerageId);
    }

    const note = await noteRepository.create({
      ...noteData,
      userId: createdBy,
      mentions,
    });

    // Get mentioned users for the response
    const mentionedUsers = mentions.length > 0 
      ? await noteRepository.getMentionedUsers([note.id])
      : [];

    // Log the creation
    logBusiness.transactionCreated(note.id, createdBy, `Note created in transaction: ${transaction.propertyAddress}`);

    // TODO: Trigger notifications for mentioned users
    if (mentions.length > 0) {
      await this.triggerMentionNotifications(note, mentionedUsers);
    }

    return this.formatNoteDetails({ ...note, mentionedUsers }, createdBy);
  }

  /**
   * Get note by ID
   */
  async getNoteById(id: string, requestingUserId: string, userBrokerageId?: string): Promise<NoteDetails> {
    const note = await noteRepository.findById(id, true);
    
    if (!note) {
      throw new AppError('Note not found', 404, 'NOTE_NOT_FOUND');
    }

    // Check access for non-admin users
    if (userBrokerageId && note.transaction.brokerageId !== userBrokerageId) {
      throw new AppError('Access denied', 403, 'ACCESS_DENIED');
    }

    // Get mentioned users
    const mentionedUsers = note.mentions.length > 0 
      ? await noteRepository.getMentionedUsers([note.id])
      : [];

    return this.formatNoteDetails({ ...note, mentionedUsers }, requestingUserId);
  }

  /**
   * Update note
   */
  async updateNote(
    id: string, 
    updateData: UpdateNoteData, 
    updatedBy: string,
    userBrokerageId: string
  ): Promise<NoteDetails> {
    // Check if note exists
    const existingNote = await noteRepository.findById(id, true);
    if (!existingNote) {
      throw new AppError('Note not found', 404, 'NOTE_NOT_FOUND');
    }

    // Check access to transaction
    if (existingNote.transaction.brokerageId !== userBrokerageId) {
      throw new AppError('Access denied', 403, 'ACCESS_DENIED');
    }

    // Only the author can edit their note
    if (existingNote.userId !== updatedBy) {
      throw new AppError('Only the author can edit this note', 403, 'INSUFFICIENT_PERMISSIONS');
    }

    // Validate update data
    this.validateNoteData(updateData, true);

    // Extract mentions from content if content is being updated
    let mentions = updateData.mentions;
    if (updateData.content && !mentions) {
      mentions = NoteValidation.extractMentions(updateData.content);
    }

    // Validate mentioned users if mentions are being updated
    if (mentions && mentions.length > 0) {
      await this.validateMentionedUsers(mentions, userBrokerageId);
    }

    const updatedNote = await noteRepository.update(id, {
      ...updateData,
      mentions,
    });

    // Get mentioned users
    const mentionedUsers = updatedNote.mentions.length > 0 
      ? await noteRepository.getMentionedUsers([updatedNote.id])
      : [];

    // Log the update
    logBusiness.transactionCreated(id, updatedBy, `Note updated in transaction: ${updatedNote.transaction.propertyAddress}`);

    // Trigger notifications for new mentions
    if (mentions && mentions.length > 0) {
      const newMentions = mentions.filter(userId => !existingNote.mentions.includes(userId));
      if (newMentions.length > 0) {
        const newMentionedUsers = mentionedUsers.filter(user => newMentions.includes(user.id));
        await this.triggerMentionNotifications(updatedNote, newMentionedUsers);
      }
    }

    return this.formatNoteDetails({ ...updatedNote, mentionedUsers }, updatedBy);
  }

  /**
   * Delete note
   */
  async deleteNote(id: string, deletedBy: string, userBrokerageId: string): Promise<void> {
    const note = await noteRepository.findById(id, true);
    if (!note) {
      throw new AppError('Note not found', 404, 'NOTE_NOT_FOUND');
    }

    // Check access
    if (note.transaction.brokerageId !== userBrokerageId) {
      throw new AppError('Access denied', 403, 'ACCESS_DENIED');
    }

    // Only the author can delete their note (or admins/TCs)
    if (note.userId !== deletedBy) {
      // Check if user has admin/TC permissions
      const user = await userRepository.findById(deletedBy);
      if (!user || ![UserRole.ADMIN, UserRole.TC].includes(user.role)) {
        throw new AppError('Only the author or administrators can delete this note', 403, 'INSUFFICIENT_PERMISSIONS');
      }
    }

    await noteRepository.delete(id);

    // Log the deletion
    logBusiness.transactionCreated(id, deletedBy, `Note deleted from transaction: ${note.transaction.propertyAddress}`);
  }

  /**
   * Get notes with search and pagination
   */
  async getNotes(
    criteria: NoteSearchCriteria,
    page = 1,
    limit = 10,
    requestingUser: { role: UserRole; brokerageId?: string; id: string }
  ): Promise<NoteListResponse> {
    // Apply role-based filtering
    const searchCriteria = { ...criteria };
    
    const result = await noteRepository.findMany(searchCriteria, page, limit);

    // Filter by brokerage for non-admin users
    let filteredNotes = result.notes;
    if (requestingUser.role !== UserRole.ADMIN && requestingUser.brokerageId) {
      filteredNotes = result.notes.filter(
        note => note.transaction.brokerageId === requestingUser.brokerageId
      );
    }

    return {
      notes: filteredNotes.map(note => this.formatNoteSummary(note)),
      total: result.total,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages,
    };
  }

  /**
   * Get note statistics
   */
  async getNoteStats(requestingUser: { role: UserRole; brokerageId?: string; id: string }): Promise<NoteStats> {
    // Apply role-based filtering
    let brokerageId: string | undefined;
    
    if (requestingUser.role !== UserRole.ADMIN && requestingUser.brokerageId) {
      brokerageId = requestingUser.brokerageId;
    }

    return noteRepository.getStats(brokerageId);
  }

  /**
   * Get notes by transaction
   */
  async getNotesByTransaction(
    transactionId: string,
    requestingUser: { role: UserRole; brokerageId?: string; id: string }
  ) {
    // Validate access to transaction
    const transaction = await transactionRepository.findById(transactionId, false);
    if (!transaction) {
      throw new AppError('Transaction not found', 404, 'TRANSACTION_NOT_FOUND');
    }

    // Check access based on role
    if (requestingUser.role !== UserRole.ADMIN && transaction.brokerageId !== requestingUser.brokerageId) {
      throw new AppError('Access denied', 403, 'ACCESS_DENIED');
    }

    const notes = await noteRepository.findByTransaction(transactionId);
    return notes.map(note => this.formatNoteSummary(note));
  }

  /**
   * Get note thread (conversation) for a transaction
   */
  async getNoteThread(
    transactionId: string,
    requestingUser: { role: UserRole; brokerageId?: string; id: string }
  ): Promise<NoteThread> {
    // Validate access to transaction
    const transaction = await transactionRepository.findById(transactionId, false);
    if (!transaction) {
      throw new AppError('Transaction not found', 404, 'TRANSACTION_NOT_FOUND');
    }

    // Check access based on role
    if (requestingUser.role !== UserRole.ADMIN && transaction.brokerageId !== requestingUser.brokerageId) {
      throw new AppError('Access denied', 403, 'ACCESS_DENIED');
    }

    const threadData = await noteRepository.getThread(transactionId);
    
    return {
      transactionId,
      propertyAddress: transaction.propertyAddress,
      notes: threadData.notes.map(note => this.formatNoteSummary(note)),
      participants: threadData.participants,
      totalNotes: threadData.totalNotes,
      lastActivity: threadData.lastActivity,
    };
  }

  /**
   * Get mentions for a user
   */
  async getUserMentions(
    userId: string,
    requestingUser: { role: UserRole; brokerageId?: string; id: string }
  ): Promise<NoteMention[]> {
    // Users can only see their own mentions unless they're admin/TC
    if (requestingUser.role !== UserRole.ADMIN && requestingUser.role !== UserRole.TC && requestingUser.id !== userId) {
      throw new AppError('Access denied', 403, 'ACCESS_DENIED');
    }

    const brokerageId = requestingUser.role !== UserRole.ADMIN ? requestingUser.brokerageId : undefined;
    const notes = await noteRepository.findMentions(userId, brokerageId);

    return notes.map(note => ({
      userId,
      userName: `${note.user.firstName} ${note.user.lastName}`,
      userEmail: note.user.email,
      noteId: note.id,
      noteContent: NoteValidation.getContentPreview(note.content),
      transactionId: note.transactionId,
      propertyAddress: note.transaction.propertyAddress,
      mentionedAt: note.createdAt,
      isRead: false, // TODO: Implement read status tracking
    }));
  }

  /**
   * Bulk update notes
   */
  async bulkUpdateNotes(
    operation: BulkNoteOperation,
    updatedBy: string,
    userBrokerageId: string
  ): Promise<void> {
    // Validate all notes exist and user has access
    for (const noteId of operation.noteIds) {
      const note = await noteRepository.findById(noteId, true);
      if (!note) {
        throw new AppError(`Note ${noteId} not found`, 404, 'NOTE_NOT_FOUND');
      }

      if (note.transaction.brokerageId !== userBrokerageId) {
        throw new AppError('Access denied to one or more notes', 403, 'ACCESS_DENIED');
      }

      // For delete operations, check if user can delete each note
      if (operation.operation === 'delete' && note.userId !== updatedBy) {
        const user = await userRepository.findById(updatedBy);
        if (!user || ![UserRole.ADMIN, UserRole.TC].includes(user.role)) {
          throw new AppError('Insufficient permissions to delete one or more notes', 403, 'INSUFFICIENT_PERMISSIONS');
        }
      }
    }

    await noteRepository.bulkUpdate(operation);

    // Log the bulk operation
    logBusiness.transactionCreated(
      operation.noteIds.join(','),
      updatedBy,
      `Bulk ${operation.operation} operation on notes`
    );
  }

  /**
   * Validate note data
   */
  private validateNoteData(data: CreateNoteData | UpdateNoteData, isUpdate = false): void {
    // Validate content if provided
    if (data.content !== undefined && !NoteValidation.isValidContent(data.content)) {
      throw new AppError('Note content must be between 1 and 2000 characters', 400, 'INVALID_CONTENT');
    }

    // For create operations, ensure content is present
    if (!isUpdate) {
      const createData = data as CreateNoteData;
      if (!createData.content?.trim()) {
        throw new AppError('Note content is required', 400, 'MISSING_CONTENT');
      }
    }
  }

  /**
   * Validate mentioned users
   */
  private async validateMentionedUsers(mentions: string[], userBrokerageId: string): Promise<void> {
    for (const userId of mentions) {
      const user = await userRepository.findById(userId);
      if (!user) {
        throw new AppError(`Mentioned user ${userId} not found`, 400, 'INVALID_MENTION');
      }
      
      // Users can only mention users from their brokerage
      if (user.brokerageId !== userBrokerageId) {
        throw new AppError('Cannot mention users from other brokerages', 400, 'INVALID_MENTION');
      }
    }
  }

  /**
   * Trigger mention notifications
   */
  private async triggerMentionNotifications(note: any, mentionedUsers: any[]): Promise<void> {
    // TODO: Implement notification system
    // This would typically send emails, push notifications, or in-app notifications
    // For now, we'll just log the mentions
    for (const user of mentionedUsers) {
      logBusiness.transactionCreated(
        note.id,
        note.userId,
        `User ${user.firstName} ${user.lastName} mentioned in note`
      );
    }
  }

  /**
   * Check if user can access note
   */
  canAccessNote(
    requestingUser: { role: UserRole; brokerageId?: string; id: string },
    note: { transaction: { brokerageId: string }; userId: string }
  ): boolean {
    // Admins can access any note
    if (requestingUser.role === UserRole.ADMIN) {
      return true;
    }

    // Users can only access notes from their brokerage
    return requestingUser.brokerageId === note.transaction.brokerageId;
  }
}

// Export singleton instance
export const noteService = new NoteService();
