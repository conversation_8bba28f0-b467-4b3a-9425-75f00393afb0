/**
 * ContactList component
 *
 * Displays a list of contacts with search, filtering, and pagination
 */

'use client';

import { useState, useEffect } from 'react';
import { ContactListProps, ContactSummary, ContactSearchCriteria } from '@/types/contact';
import { ContactCard } from './ContactCard';
// import { ContactFilter } from './ContactFilter'; // TODO: Create ContactFilter component
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

// Icons
const SearchIcon = () => <span>🔍</span>;
const FilterIcon = () => <span>🔽</span>;
const AddIcon = () => <span>➕</span>;

interface ContactListState {
  contacts: ContactSummary[];
  loading: boolean;
  error: string | null;
  total: number;
  page: number;
  totalPages: number;
}

export function ContactList({
  transactionId,
  searchCriteria = {},
  onContactSelect,
  onContactEdit,
  onContactDelete,
  showActions = true,
  compact = false,
}: ContactListProps) {
  const [state, setState] = useState<ContactListState>({
    contacts: [],
    loading: true,
    error: null,
    total: 0,
    page: 1,
    totalPages: 1,
  });

  const [criteria, setCriteria] = useState<ContactSearchCriteria>({
    ...searchCriteria,
    transactionId,
    page: 1,
    limit: 10,
  });

  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  // Mock data for development
  const mockContacts: ContactSummary[] = [
    {
      id: '1',
      transactionId: transactionId || 'trans-1',
      firstName: 'John',
      lastName: 'Smith',
      email: '<EMAIL>',
      phone: '(*************',
      role: 'buyer',
      company: 'ABC Realty',
      createdAt: new Date().toISOString(),
      transaction: {
        id: transactionId || 'trans-1',
        propertyAddress: '123 Main St, Anytown, PA 12345',
      },
      fullName: 'John Smith',
      displayName: 'John Smith (ABC Realty)',
    },
    {
      id: '2',
      transactionId: transactionId || 'trans-1',
      firstName: 'Jane',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '(*************',
      role: 'attorney',
      company: 'Legal Associates',
      createdAt: new Date().toISOString(),
      transaction: {
        id: transactionId || 'trans-1',
        propertyAddress: '123 Main St, Anytown, PA 12345',
      },
      fullName: 'Jane Doe',
      displayName: 'Jane Doe (Legal Associates)',
    },
  ];

  useEffect(() => {
    loadContacts();
  }, [criteria]);

  const loadContacts = async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      // TODO: Replace with actual API call
      // const response = await contactApi.getContacts(criteria);

      // Mock API response
      await new Promise(resolve => setTimeout(resolve, 500));

      setState(prev => ({
        ...prev,
        contacts: mockContacts,
        total: mockContacts.length,
        totalPages: 1,
        loading: false,
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: 'Failed to load contacts',
        loading: false,
      }));
    }
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setCriteria(prev => ({
      ...prev,
      search: term || undefined,
      page: 1,
    }));
  };

  const handleCriteriaChange = (newCriteria: ContactSearchCriteria) => {
    setCriteria(prev => ({
      ...prev,
      ...newCriteria,
      page: 1,
    }));
  };

  const handlePageChange = (page: number) => {
    setCriteria(prev => ({ ...prev, page }));
  };

  const handleContactSelect = (contact: ContactSummary) => {
    if (onContactSelect) {
      onContactSelect(contact as any); // Type conversion for compatibility
    }
  };

  const handleContactEdit = (contact: ContactSummary) => {
    if (onContactEdit) {
      onContactEdit(contact as any); // Type conversion for compatibility
    }
  };

  const handleContactDelete = async (contactId: string) => {
    if (onContactDelete) {
      await onContactDelete(contactId);
      // Reload contacts after deletion
      loadContacts();
    }
  };

  const resetFilters = () => {
    setSearchTerm('');
    setCriteria({
      transactionId,
      page: 1,
      limit: 10,
    });
    setShowFilters(false);
  };

  if (state.loading && state.contacts.length === 0) {
    return (
      <div className="flex items-center justify-center py-8">
        <LoadingSpinner size="lg" />
        <span className="ml-2 text-gray-600">Loading contacts...</span>
      </div>
    );
  }

  if (state.error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <p className="text-red-600">{state.error}</p>
        <Button
          variant="outline"
          size="sm"
          onClick={loadContacts}
          className="mt-2"
        >
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <SearchIcon />
            </div>
            <Input
              type="text"
              placeholder="Search contacts..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            leftIcon={<FilterIcon />}
          >
            Filters
          </Button>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="bg-gray-50 rounded-lg p-4">
          <p className="text-sm text-gray-600">Contact filters will be implemented here</p>
          {/* TODO: Implement ContactFilter component */}
        </div>
      )}

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-gray-600">
          {state.total === 0 ? 'No contacts found' : `${state.total} contact${state.total === 1 ? '' : 's'} found`}
        </p>

        {state.loading && (
          <LoadingSpinner size="sm" />
        )}
      </div>

      {/* Contact Grid */}
      {state.contacts.length > 0 ? (
        <div className={`grid gap-4 ${compact ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1 lg:grid-cols-2'}`}>
          {state.contacts.map((contact) => (
            <ContactCard
              key={contact.id}
              contact={contact}
              onEdit={showActions ? handleContactEdit : undefined}
              onDelete={showActions ? handleContactDelete : undefined}
              onView={handleContactSelect}
              showActions={showActions}
              compact={compact}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <p className="text-gray-500 mb-4">No contacts found</p>
          {transactionId && (
            <p className="text-sm text-gray-400">
              Add contacts to this transaction to get started
            </p>
          )}
        </div>
      )}

      {/* Pagination */}
      {state.totalPages > 1 && (
        <div className="flex items-center justify-center space-x-2 pt-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(state.page - 1)}
            disabled={state.page === 1 || state.loading}
          >
            Previous
          </Button>

          <span className="text-sm text-gray-600">
            Page {state.page} of {state.totalPages}
          </span>

          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(state.page + 1)}
            disabled={state.page === state.totalPages || state.loading}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );
}
