/**
 * Notes and collaboration routes
 *
 * Handles notes, comments, and collaboration features
 */

import express from 'express';
import { UserRole } from '@prisma/client';
import { asyncHandler, AppError } from '../middleware/errorHandler';
import { authenticate, authorize, AuthenticatedRequest } from '../middleware/auth';
import { noteService } from '../services/noteService';
import { CreateNoteData, UpdateNoteData, NoteSearchCriteria, BulkNoteOperation } from '../models/Note';
import { ValidationHelpers, ValidationPatterns } from '../utils/validation';
import Joi from 'joi';

const router = express.Router();

/**
 * Validation schemas
 */
const createNoteSchema = Joi.object({
  transactionId: ValidationPatterns.uuid.required(),
  content: Joi.string().min(1).max(2000).required(),
  mentions: Joi.array().items(ValidationPatterns.uuid).optional(),
});

const updateNoteSchema = Joi.object({
  content: Joi.string().min(1).max(2000).optional(),
  mentions: Joi.array().items(ValidationPatterns.uuid).optional(),
});

const noteSearchSchema = Joi.object({
  transactionId: ValidationPatterns.uuid.optional(),
  userId: ValidationPatterns.uuid.optional(),
  mentionedUserId: ValidationPatterns.uuid.optional(),
  search: ValidationPatterns.search.optional(),
  dateFrom: ValidationPatterns.date.optional(),
  dateTo: ValidationPatterns.date.optional(),
  sortBy: Joi.string().valid('createdAt', 'updatedAt', 'content').optional(),
  sortOrder: Joi.string().valid('asc', 'desc').optional(),
  page: ValidationPatterns.page,
  limit: ValidationPatterns.limit,
});

const bulkNoteSchema = Joi.object({
  noteIds: Joi.array().items(ValidationPatterns.uuid).min(1).required(),
  operation: Joi.string().valid('delete', 'export').required(),
});

/**
 * All note routes require authentication
 */
router.use(authenticate);

/**
 * GET /api/notes
 * Get notes with filtering and pagination
 */
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  // Validate query parameters
  const { page, limit, ...searchCriteria } = ValidationHelpers.validateQuery(noteSearchSchema, req.query);

  const result = await noteService.getNotes(
    searchCriteria as NoteSearchCriteria,
    page,
    limit,
    req.user!
  );

  res.json({
    message: 'Notes retrieved successfully',
    data: result,
  });
}));

/**
 * GET /api/notes/stats
 * Get note statistics
 */
router.get('/stats', authorize(UserRole.ADMIN, UserRole.TC, UserRole.BROKER), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const stats = await noteService.getNoteStats(req.user!);

  res.json({
    message: 'Note statistics retrieved successfully',
    data: stats,
  });
}));

/**
 * POST /api/notes
 * Create new note
 */
router.post('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  // Validate request body
  const noteData: CreateNoteData = ValidationHelpers.validateBody(createNoteSchema, req.body);

  const note = await noteService.createNote(
    noteData,
    req.user!.id,
    req.user!.brokerageId!
  );

  res.status(201).json({
    message: 'Note created successfully',
    data: note,
  });
}));

/**
 * POST /api/notes/bulk
 * Bulk update notes
 */
router.post('/bulk', authorize(UserRole.TC, UserRole.ADMIN), asyncHandler(async (req: AuthenticatedRequest, res) => {
  // Validate request body
  const operation: BulkNoteOperation = ValidationHelpers.validateBody(bulkNoteSchema, req.body);

  await noteService.bulkUpdateNotes(
    operation,
    req.user!.id,
    req.user!.brokerageId!
  );

  res.json({
    message: 'Bulk note operation completed successfully',
  });
}));

/**
 * GET /api/notes/:id
 * Get note details
 */
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  // Get note with permission check
  const note = await noteService.getNoteById(id, req.user!.id, req.user!.brokerageId);

  res.json({
    message: 'Note retrieved successfully',
    data: note,
  });
}));

/**
 * PUT /api/notes/:id
 * Update note (author only)
 */
router.put('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  // Validate request body
  const updateData: UpdateNoteData = ValidationHelpers.validateBody(updateNoteSchema, req.body);

  // Update note
  const updatedNote = await noteService.updateNote(
    id,
    updateData,
    req.user!.id,
    req.user!.brokerageId!
  );

  res.json({
    message: 'Note updated successfully',
    data: updatedNote,
  });
}));

/**
 * DELETE /api/notes/:id
 * Delete note (author or TC/Admin only)
 */
router.delete('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  await noteService.deleteNote(id, req.user!.id, req.user!.brokerageId!);

  res.json({
    message: 'Note deleted successfully',
  });
}));

/**
 * GET /api/notes/transaction/:transactionId
 * Get notes by transaction
 */
router.get('/transaction/:transactionId', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { transactionId } = req.params;

  const notes = await noteService.getNotesByTransaction(transactionId, req.user!);

  res.json({
    message: 'Transaction notes retrieved successfully',
    data: notes,
  });
}));

/**
 * GET /api/notes/transaction/:transactionId/thread
 * Get note thread (conversation) for a transaction
 */
router.get('/transaction/:transactionId/thread', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { transactionId } = req.params;

  const thread = await noteService.getNoteThread(transactionId, req.user!);

  res.json({
    message: 'Note thread retrieved successfully',
    data: thread,
  });
}));

/**
 * GET /api/notes/mentions/:userId
 * Get mentions for a user
 */
router.get('/mentions/:userId', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { userId } = req.params;

  const mentions = await noteService.getUserMentions(userId, req.user!);

  res.json({
    message: 'User mentions retrieved successfully',
    data: mentions,
  });
}));

export default router;
