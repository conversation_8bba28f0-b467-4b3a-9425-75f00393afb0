/**
 * Note repository
 * 
 * Data access layer for note operations and communication features
 */

import { Prisma } from '@prisma/client';
import { prisma } from '../index';
import { CreateNoteData, UpdateNoteData, NoteSearchCriteria, BulkNoteOperation } from '../models/Note';

/**
 * Note repository class for database operations
 */
export class NoteRepository {
  /**
   * Find note by ID with optional includes
   */
  async findById(id: string, includeRelations = true) {
    return prisma.note.findUnique({
      where: { id },
      include: includeRelations ? {
        transaction: {
          select: {
            id: true,
            propertyAddress: true,
            status: true,
            brokerageId: true,
          },
        },
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      } : false,
    });
  }

  /**
   * Create new note
   */
  async create(noteData: CreateNoteData & { userId: string }) {
    return prisma.note.create({
      data: noteData,
      include: {
        transaction: {
          select: {
            id: true,
            propertyAddress: true,
            status: true,
            brokerageId: true,
          },
        },
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });
  }

  /**
   * Update note by ID
   */
  async update(id: string, updateData: UpdateNoteData) {
    return prisma.note.update({
      where: { id },
      data: updateData,
      include: {
        transaction: {
          select: {
            id: true,
            propertyAddress: true,
            status: true,
            brokerageId: true,
          },
        },
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });
  }

  /**
   * Delete note by ID
   */
  async delete(id: string) {
    return prisma.note.delete({
      where: { id },
    });
  }

  /**
   * Find notes with search criteria and pagination
   */
  async findMany(criteria: NoteSearchCriteria, page = 1, limit = 10) {
    const skip = (page - 1) * limit;
    
    // Build where clause
    const where: Prisma.NoteWhereInput = {};
    
    if (criteria.transactionId) {
      where.transactionId = criteria.transactionId;
    }
    
    if (criteria.userId) {
      where.userId = criteria.userId;
    }
    
    if (criteria.mentionedUserId) {
      where.mentions = {
        has: criteria.mentionedUserId,
      };
    }
    
    if (criteria.search) {
      where.content = {
        contains: criteria.search,
        mode: 'insensitive',
      };
    }
    
    if (criteria.dateFrom || criteria.dateTo) {
      where.createdAt = {};
      if (criteria.dateFrom) {
        where.createdAt.gte = criteria.dateFrom;
      }
      if (criteria.dateTo) {
        where.createdAt.lte = criteria.dateTo;
      }
    }

    // Build order by clause
    const orderBy: Prisma.NoteOrderByWithRelationInput = {};
    if (criteria.sortBy) {
      switch (criteria.sortBy) {
        case 'content':
          orderBy.content = criteria.sortOrder || 'asc';
          break;
        case 'updatedAt':
          orderBy.updatedAt = criteria.sortOrder || 'desc';
          break;
        case 'createdAt':
        default:
          orderBy.createdAt = criteria.sortOrder || 'desc';
          break;
      }
    } else {
      // Default sort: newest first
      orderBy.createdAt = 'desc';
    }

    // Get total count and notes
    const [total, notes] = await Promise.all([
      prisma.note.count({ where }),
      prisma.note.findMany({
        where,
        skip,
        take: limit,
        orderBy,
        include: {
          transaction: {
            select: {
              id: true,
              propertyAddress: true,
            },
          },
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      }),
    ]);

    return {
      notes,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Get note statistics
   */
  async getStats(brokerageId?: string) {
    // Build base where clause
    const baseWhere: Prisma.NoteWhereInput = {};
    
    if (brokerageId) {
      baseWhere.transaction = { brokerageId };
    }

    const [
      total,
      userCounts,
      transactionCounts,
      totalMentions,
      recentActivity,
      mostActiveUsers,
    ] = await Promise.all([
      prisma.note.count({ where: baseWhere }),
      
      prisma.note.groupBy({
        by: ['userId'],
        where: baseWhere,
        _count: {
          userId: true,
        },
      }),
      
      prisma.note.groupBy({
        by: ['transactionId'],
        where: baseWhere,
        _count: {
          transactionId: true,
        },
      }),
      
      // Count total mentions across all notes
      prisma.note.findMany({
        where: baseWhere,
        select: {
          mentions: true,
        },
      }).then(notes => 
        notes.reduce((total, note) => total + note.mentions.length, 0)
      ),
      
      prisma.note.count({
        where: {
          ...baseWhere,
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
          },
        },
      }),
      
      // Get most active users with their details
      prisma.note.groupBy({
        by: ['userId'],
        where: baseWhere,
        _count: {
          userId: true,
        },
        orderBy: {
          _count: {
            userId: 'desc',
          },
        },
        take: 5,
      }).then(async (userCounts) => {
        const userIds = userCounts.map(uc => uc.userId);
        const users = await prisma.user.findMany({
          where: { id: { in: userIds } },
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        });
        
        return userCounts.map(uc => {
          const user = users.find(u => u.id === uc.userId);
          return {
            userId: uc.userId,
            userName: user ? `${user.firstName} ${user.lastName}` : 'Unknown User',
            noteCount: uc._count.userId,
          };
        });
      }),
    ]);

    // Convert user counts to object
    const byUser = userCounts.reduce((acc, item) => {
      acc[item.userId] = item._count.userId;
      return acc;
    }, {} as Record<string, number>);

    // Convert transaction counts to object
    const byTransaction = transactionCounts.reduce((acc, item) => {
      acc[item.transactionId] = item._count.transactionId;
      return acc;
    }, {} as Record<string, number>);

    // Calculate average notes per transaction
    const transactionCount = Object.keys(byTransaction).length;
    const averageNotesPerTransaction = transactionCount > 0 ? Math.round(total / transactionCount * 100) / 100 : 0;

    return {
      total,
      byUser,
      byTransaction,
      totalMentions,
      recentActivity,
      averageNotesPerTransaction,
      mostActiveUsers,
    };
  }

  /**
   * Get notes by transaction
   */
  async findByTransaction(transactionId: string) {
    return prisma.note.findMany({
      where: { transactionId },
      orderBy: { createdAt: 'asc' },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });
  }

  /**
   * Get notes by user
   */
  async findByUser(userId: string, brokerageId?: string) {
    const where: Prisma.NoteWhereInput = { userId };
    
    if (brokerageId) {
      where.transaction = { brokerageId };
    }

    return prisma.note.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      include: {
        transaction: {
          select: {
            id: true,
            propertyAddress: true,
          },
        },
      },
    });
  }

  /**
   * Get notes where user is mentioned
   */
  async findMentions(userId: string, brokerageId?: string, isRead?: boolean) {
    const where: Prisma.NoteWhereInput = {
      mentions: {
        has: userId,
      },
    };
    
    if (brokerageId) {
      where.transaction = { brokerageId };
    }

    return prisma.note.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      include: {
        transaction: {
          select: {
            id: true,
            propertyAddress: true,
          },
        },
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });
  }

  /**
   * Get recent notes
   */
  async findRecent(limit = 10, brokerageId?: string) {
    const where: Prisma.NoteWhereInput = {};
    
    if (brokerageId) {
      where.transaction = { brokerageId };
    }

    return prisma.note.findMany({
      where,
      take: limit,
      orderBy: { createdAt: 'desc' },
      include: {
        transaction: {
          select: {
            id: true,
            propertyAddress: true,
          },
        },
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });
  }

  /**
   * Bulk update notes
   */
  async bulkUpdate(operation: BulkNoteOperation) {
    const { noteIds, operation: op } = operation;
    
    switch (op) {
      case 'delete':
        return prisma.note.deleteMany({
          where: { id: { in: noteIds } },
        });
        
      default:
        throw new Error(`Unsupported bulk operation: ${op}`);
    }
  }

  /**
   * Get note thread (conversation) for a transaction
   */
  async getThread(transactionId: string) {
    const [notes, participants] = await Promise.all([
      this.findByTransaction(transactionId),
      
      // Get unique participants in the conversation
      prisma.note.findMany({
        where: { transactionId },
        select: {
          userId: true,
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          createdAt: true,
        },
        orderBy: { createdAt: 'desc' },
      }).then(notes => {
        const userMap = new Map();
        notes.forEach(note => {
          if (!userMap.has(note.userId)) {
            userMap.set(note.userId, {
              ...note.user,
              noteCount: 1,
              lastActivity: note.createdAt,
            });
          } else {
            const user = userMap.get(note.userId);
            user.noteCount++;
            if (note.createdAt > user.lastActivity) {
              user.lastActivity = note.createdAt;
            }
          }
        });
        return Array.from(userMap.values());
      }),
    ]);

    return {
      notes,
      participants,
      totalNotes: notes.length,
      lastActivity: notes.length > 0 ? notes[notes.length - 1].createdAt : null,
    };
  }

  /**
   * Get users mentioned in notes for a transaction
   */
  async getMentionedUsers(noteIds: string[]) {
    const notes = await prisma.note.findMany({
      where: { id: { in: noteIds } },
      select: { mentions: true },
    });

    const mentionedUserIds = [...new Set(notes.flatMap(note => note.mentions))];
    
    if (mentionedUserIds.length === 0) {
      return [];
    }

    return prisma.user.findMany({
      where: { id: { in: mentionedUserIds } },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
      },
    });
  }
}

// Export singleton instance
export const noteRepository = new NoteRepository();
