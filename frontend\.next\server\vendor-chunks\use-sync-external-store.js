"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/use-sync-external-store";
exports.ids = ["vendor-chunks/use-sync-external-store"];
exports.modules = {

/***/ "(ssr)/./node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */ \n true && function() {\n    function is(x, y) {\n        return x === y && (0 !== x || 1 / x === 1 / y) || x !== x && y !== y;\n    }\n    function useSyncExternalStore$2(subscribe, getSnapshot) {\n        didWarnOld18Alpha || void 0 === React.startTransition || (didWarnOld18Alpha = !0, console.error(\"You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release.\"));\n        var value = getSnapshot();\n        if (!didWarnUncachedGetSnapshot) {\n            var cachedValue = getSnapshot();\n            objectIs(value, cachedValue) || (console.error(\"The result of getSnapshot should be cached to avoid an infinite loop\"), didWarnUncachedGetSnapshot = !0);\n        }\n        cachedValue = useState({\n            inst: {\n                value: value,\n                getSnapshot: getSnapshot\n            }\n        });\n        var inst = cachedValue[0].inst, forceUpdate = cachedValue[1];\n        useLayoutEffect(function() {\n            inst.value = value;\n            inst.getSnapshot = getSnapshot;\n            checkIfSnapshotChanged(inst) && forceUpdate({\n                inst: inst\n            });\n        }, [\n            subscribe,\n            value,\n            getSnapshot\n        ]);\n        useEffect(function() {\n            checkIfSnapshotChanged(inst) && forceUpdate({\n                inst: inst\n            });\n            return subscribe(function() {\n                checkIfSnapshotChanged(inst) && forceUpdate({\n                    inst: inst\n                });\n            });\n        }, [\n            subscribe\n        ]);\n        useDebugValue(value);\n        return value;\n    }\n    function checkIfSnapshotChanged(inst) {\n        var latestGetSnapshot = inst.getSnapshot;\n        inst = inst.value;\n        try {\n            var nextValue = latestGetSnapshot();\n            return !objectIs(inst, nextValue);\n        } catch (error) {\n            return !0;\n        }\n    }\n    function useSyncExternalStore$1(subscribe, getSnapshot) {\n        return getSnapshot();\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && \"function\" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"), objectIs = \"function\" === typeof Object.is ? Object.is : is, useState = React.useState, useEffect = React.useEffect, useLayoutEffect = React.useLayoutEffect, useDebugValue = React.useDebugValue, didWarnOld18Alpha = !1, didWarnUncachedGetSnapshot = !1, shim =  true ? useSyncExternalStore$1 : 0;\n    exports.useSyncExternalStore = void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && \"function\" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n}();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/**\n * @license React\n * use-sync-external-store-shim/with-selector.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */ \n true && function() {\n    function is(x, y) {\n        return x === y && (0 !== x || 1 / x === 1 / y) || x !== x && y !== y;\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && \"function\" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"), shim = __webpack_require__(/*! use-sync-external-store/shim */ \"(ssr)/./node_modules/use-sync-external-store/shim/index.js\"), objectIs = \"function\" === typeof Object.is ? Object.is : is, useSyncExternalStore = shim.useSyncExternalStore, useRef = React.useRef, useEffect = React.useEffect, useMemo = React.useMemo, useDebugValue = React.useDebugValue;\n    exports.useSyncExternalStoreWithSelector = function(subscribe, getSnapshot, getServerSnapshot, selector, isEqual) {\n        var instRef = useRef(null);\n        if (null === instRef.current) {\n            var inst = {\n                hasValue: !1,\n                value: null\n            };\n            instRef.current = inst;\n        } else inst = instRef.current;\n        instRef = useMemo(function() {\n            function memoizedSelector(nextSnapshot) {\n                if (!hasMemo) {\n                    hasMemo = !0;\n                    memoizedSnapshot = nextSnapshot;\n                    nextSnapshot = selector(nextSnapshot);\n                    if (void 0 !== isEqual && inst.hasValue) {\n                        var currentSelection = inst.value;\n                        if (isEqual(currentSelection, nextSnapshot)) return memoizedSelection = currentSelection;\n                    }\n                    return memoizedSelection = nextSnapshot;\n                }\n                currentSelection = memoizedSelection;\n                if (objectIs(memoizedSnapshot, nextSnapshot)) return currentSelection;\n                var nextSelection = selector(nextSnapshot);\n                if (void 0 !== isEqual && isEqual(currentSelection, nextSelection)) return memoizedSnapshot = nextSnapshot, currentSelection;\n                memoizedSnapshot = nextSnapshot;\n                return memoizedSelection = nextSelection;\n            }\n            var hasMemo = !1, memoizedSnapshot, memoizedSelection, maybeGetServerSnapshot = void 0 === getServerSnapshot ? null : getServerSnapshot;\n            return [\n                function() {\n                    return memoizedSelector(getSnapshot());\n                },\n                null === maybeGetServerSnapshot ? void 0 : function() {\n                    return memoizedSelector(maybeGetServerSnapshot());\n                }\n            ];\n        }, [\n            getSnapshot,\n            getServerSnapshot,\n            selector,\n            isEqual\n        ]);\n        var value = useSyncExternalStore(subscribe, instRef[0], instRef[1]);\n        useEffect(function() {\n            inst.hasValue = !0;\n            inst.value = value;\n        }, [\n            value\n        ]);\n        useDebugValue(value);\n        return value;\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && \"function\" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n}();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-sync-external-store/shim/index.js":
/*!************************************************************!*\
  !*** ./node_modules/use-sync-external-store/shim/index.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nif (false) {} else {\n    module.exports = __webpack_require__(/*! ../cjs/use-sync-external-store-shim.development.js */ \"(ssr)/./node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLXN5bmMtZXh0ZXJuYWwtc3RvcmUvc2hpbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUVBLElBQUlBLEtBQXlCLEVBQWMsRUFFMUMsTUFBTTtJQUNMQyw4TEFBeUI7QUFDM0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90Yy1wbGF0Zm9ybS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy91c2Utc3luYy1leHRlcm5hbC1zdG9yZS9zaGltL2luZGV4LmpzPzJlODciXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4uL2Nqcy91c2Utc3luYy1leHRlcm5hbC1zdG9yZS1zaGltLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi4vY2pzL3VzZS1zeW5jLWV4dGVybmFsLXN0b3JlLXNoaW0uZGV2ZWxvcG1lbnQuanMnKTtcbn1cbiJdLCJuYW1lcyI6WyJwcm9jZXNzIiwibW9kdWxlIiwiZXhwb3J0cyIsInJlcXVpcmUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-sync-external-store/shim/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-sync-external-store/shim/with-selector.js":
/*!********************************************************************!*\
  !*** ./node_modules/use-sync-external-store/shim/with-selector.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nif (false) {} else {\n    module.exports = __webpack_require__(/*! ../cjs/use-sync-external-store-shim/with-selector.development.js */ \"(ssr)/./node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLXN5bmMtZXh0ZXJuYWwtc3RvcmUvc2hpbS93aXRoLXNlbGVjdG9yLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBRUEsSUFBSUEsS0FBeUIsRUFBYyxFQUUxQyxNQUFNO0lBQ0xDLDBOQUF5QjtBQUMzQiIsInNvdXJjZXMiOlsid2VicGFjazovL3RjLXBsYXRmb3JtLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3VzZS1zeW5jLWV4dGVybmFsLXN0b3JlL3NoaW0vd2l0aC1zZWxlY3Rvci5qcz9lMzNiIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuLi9janMvdXNlLXN5bmMtZXh0ZXJuYWwtc3RvcmUtc2hpbS93aXRoLXNlbGVjdG9yLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi4vY2pzL3VzZS1zeW5jLWV4dGVybmFsLXN0b3JlLXNoaW0vd2l0aC1zZWxlY3Rvci5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbInByb2Nlc3MiLCJtb2R1bGUiLCJleHBvcnRzIiwicmVxdWlyZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-sync-external-store/shim/with-selector.js\n");

/***/ })

};
;