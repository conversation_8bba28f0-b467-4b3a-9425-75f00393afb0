# TC Platform Windows Setup Script
# Run this script as Administrator in PowerShell

param(
    [string]$DatabasePassword = "tc_password_2024",
    [string]$JwtSecret = "",
    [switch]$SkipPrerequisites = $false,
    [switch]$Help = $false
)

if ($Help) {
    Write-Host @"
TC Platform Windows Setup Script

Usage: .\setup-windows.ps1 [options]

Options:
  -DatabasePassword    Set custom database password (default: tc_password_2024)
  -JwtSecret          Set custom JWT secret (auto-generated if not provided)
  -SkipPrerequisites  Skip installation of Node.js and PostgreSQL
  -Help               Show this help message

Examples:
  .\setup-windows.ps1
  .\setup-windows.ps1 -DatabasePassword "mypassword123"
  .\setup-windows.ps1 -SkipPrerequisites
"@
    exit 0
}

# Colors for output
$ErrorColor = "Red"
$SuccessColor = "Green"
$InfoColor = "Cyan"
$WarningColor = "Yellow"

function Write-Step {
    param([string]$Message)
    Write-Host "`n🔄 $Message" -ForegroundColor $InfoColor
}

function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor $SuccessColor
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor $ErrorColor
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠️  $Message" -ForegroundColor $WarningColor
}

function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Install-Chocolatey {
    Write-Step "Installing Chocolatey package manager..."
    try {
        Set-ExecutionPolicy Bypass -Scope Process -Force
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
        Write-Success "Chocolatey installed successfully"
        return $true
    }
    catch {
        Write-Error "Failed to install Chocolatey: $($_.Exception.Message)"
        return $false
    }
}

function Install-Prerequisites {
    Write-Step "Installing prerequisites..."
    
    # Check if Chocolatey is installed
    if (!(Get-Command choco -ErrorAction SilentlyContinue)) {
        if (!(Install-Chocolatey)) {
            return $false
        }
        # Refresh environment variables
        $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
    }

    # Install Node.js
    Write-Step "Installing Node.js..."
    try {
        choco install nodejs -y
        Write-Success "Node.js installed"
    }
    catch {
        Write-Error "Failed to install Node.js: $($_.Exception.Message)"
        return $false
    }

    # Install PostgreSQL
    Write-Step "Installing PostgreSQL..."
    try {
        choco install postgresql --params "/Password:$DatabasePassword" -y
        Write-Success "PostgreSQL installed"
    }
    catch {
        Write-Error "Failed to install PostgreSQL: $($_.Exception.Message)"
        return $false
    }

    # Install Git (if not present)
    if (!(Get-Command git -ErrorAction SilentlyContinue)) {
        Write-Step "Installing Git..."
        try {
            choco install git -y
            Write-Success "Git installed"
        }
        catch {
            Write-Error "Failed to install Git: $($_.Exception.Message)"
            return $false
        }
    }

    Write-Success "All prerequisites installed successfully"
    return $true
}

function Wait-ForPostgreSQL {
    Write-Step "Waiting for PostgreSQL to start..."
    $maxAttempts = 30
    $attempt = 0
    
    do {
        $attempt++
        try {
            $result = & "C:\Program Files\PostgreSQL\15\bin\pg_isready.exe" -h localhost -p 5432 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Success "PostgreSQL is ready"
                return $true
            }
        }
        catch {
            # Continue trying
        }
        
        if ($attempt -lt $maxAttempts) {
            Write-Host "Attempt $attempt/$maxAttempts - PostgreSQL not ready yet, waiting 2 seconds..." -ForegroundColor $WarningColor
            Start-Sleep -Seconds 2
        }
    } while ($attempt -lt $maxAttempts)
    
    Write-Error "PostgreSQL failed to start after $maxAttempts attempts"
    return $false
}

function Setup-Database {
    Write-Step "Setting up database..."
    
    # Start PostgreSQL service
    try {
        Start-Service postgresql-x64-15 -ErrorAction SilentlyContinue
        Write-Success "PostgreSQL service started"
    }
    catch {
        Write-Warning "Could not start PostgreSQL service automatically. Please start it manually."
    }

    # Wait for PostgreSQL to be ready
    if (!(Wait-ForPostgreSQL)) {
        return $false
    }

    # Create database and user
    $sqlCommands = @"
CREATE DATABASE tc_platform;
CREATE USER tc_user WITH PASSWORD '$DatabasePassword';
GRANT ALL PRIVILEGES ON DATABASE tc_platform TO tc_user;
ALTER USER tc_user CREATEDB;
"@

    try {
        $sqlCommands | & "C:\Program Files\PostgreSQL\15\bin\psql.exe" -U postgres -h localhost
        Write-Success "Database and user created successfully"
        return $true
    }
    catch {
        Write-Error "Failed to create database: $($_.Exception.Message)"
        return $false
    }
}

function Install-Dependencies {
    Write-Step "Installing project dependencies..."
    
    # Backend dependencies
    Write-Step "Installing backend dependencies..."
    try {
        Set-Location "backend"
        npm install
        Write-Success "Backend dependencies installed"
        Set-Location ".."
    }
    catch {
        Write-Error "Failed to install backend dependencies: $($_.Exception.Message)"
        return $false
    }

    # Frontend dependencies
    Write-Step "Installing frontend dependencies..."
    try {
        Set-Location "frontend"
        npm install
        Write-Success "Frontend dependencies installed"
        Set-Location ".."
    }
    catch {
        Write-Error "Failed to install frontend dependencies: $($_.Exception.Message)"
        return $false
    }

    return $true
}

function Generate-JwtSecret {
    if ([string]::IsNullOrEmpty($JwtSecret)) {
        $bytes = New-Object byte[] 32
        [System.Security.Cryptography.RNGCryptoServiceProvider]::Create().GetBytes($bytes)
        return [Convert]::ToBase64String($bytes)
    }
    return $JwtSecret
}

function Create-EnvironmentFiles {
    Write-Step "Creating environment files..."
    
    $generatedJwtSecret = Generate-JwtSecret
    
    # Backend .env
    $backendEnv = @"
# Database
DATABASE_URL="postgresql://tc_user:$DatabasePassword@localhost:5432/tc_platform"

# JWT
JWT_SECRET="$generatedJwtSecret"
JWT_EXPIRES_IN="7d"

# Server
PORT=3001
NODE_ENV=development

# CORS
FRONTEND_URL="http://localhost:3000"

# Logging
LOG_LEVEL=info
"@

    try {
        $backendEnv | Out-File -FilePath "backend\.env" -Encoding UTF8
        Write-Success "Backend .env file created"
    }
    catch {
        Write-Error "Failed to create backend .env file: $($_.Exception.Message)"
        return $false
    }

    # Frontend .env.local
    $frontendEnv = @"
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Environment
NODE_ENV=development
"@

    try {
        $frontendEnv | Out-File -FilePath "frontend\.env.local" -Encoding UTF8
        Write-Success "Frontend .env.local file created"
    }
    catch {
        Write-Error "Failed to create frontend .env.local file: $($_.Exception.Message)"
        return $false
    }

    return $true
}

function Setup-Database-Schema {
    Write-Step "Setting up database schema..."
    
    try {
        Set-Location "backend"
        
        # Generate Prisma client
        Write-Step "Generating Prisma client..."
        npx prisma generate
        Write-Success "Prisma client generated"
        
        # Run migrations
        Write-Step "Running database migrations..."
        npx prisma migrate dev --name init
        Write-Success "Database migrations completed"
        
        # Seed database
        Write-Step "Seeding database with sample data..."
        npx prisma db seed
        Write-Success "Database seeded with sample data"
        
        Set-Location ".."
        return $true
    }
    catch {
        Write-Error "Failed to setup database schema: $($_.Exception.Message)"
        Set-Location ".."
        return $false
    }
}

function Create-StartupScripts {
    Write-Step "Creating startup scripts..."
    
    # Create start-backend.bat
    $startBackend = @"
@echo off
echo Starting TC Platform Backend...
cd backend
npm run dev
pause
"@

    try {
        $startBackend | Out-File -FilePath "start-backend.bat" -Encoding ASCII
        Write-Success "start-backend.bat created"
    }
    catch {
        Write-Error "Failed to create start-backend.bat: $($_.Exception.Message)"
    }

    # Create start-frontend.bat
    $startFrontend = @"
@echo off
echo Starting TC Platform Frontend...
cd frontend
npm run dev
pause
"@

    try {
        $startFrontend | Out-File -FilePath "start-frontend.bat" -Encoding ASCII
        Write-Success "start-frontend.bat created"
    }
    catch {
        Write-Error "Failed to create start-frontend.bat: $($_.Exception.Message)"
    }

    # Create start-both.bat
    $startBoth = @"
@echo off
echo Starting TC Platform (Backend and Frontend)...
echo.
echo Starting Backend...
start "TC Platform Backend" cmd /k "cd backend && npm run dev"
timeout /t 5 /nobreak > nul
echo Starting Frontend...
start "TC Platform Frontend" cmd /k "cd frontend && npm run dev"
echo.
echo Both services are starting...
echo Backend: http://localhost:3001
echo Frontend: http://localhost:3000
echo.
pause
"@

    try {
        $startBoth | Out-File -FilePath "start-both.bat" -Encoding ASCII
        Write-Success "start-both.bat created"
    }
    catch {
        Write-Error "Failed to create start-both.bat: $($_.Exception.Message)"
    }
}

function Show-CompletionMessage {
    Write-Host "`n" -NoNewline
    Write-Host "🎉 TC Platform Setup Complete!" -ForegroundColor $SuccessColor
    Write-Host "`n" -NoNewline
    Write-Host "📋 What was installed:" -ForegroundColor $InfoColor
    Write-Host "   ✅ Node.js and npm"
    Write-Host "   ✅ PostgreSQL database"
    Write-Host "   ✅ Project dependencies"
    Write-Host "   ✅ Database schema and sample data"
    Write-Host "   ✅ Environment configuration"
    Write-Host "   ✅ Startup scripts"
    Write-Host "`n" -NoNewline
    Write-Host "🚀 How to start the application:" -ForegroundColor $InfoColor
    Write-Host "   Option 1: Double-click 'start-both.bat' (starts both backend and frontend)"
    Write-Host "   Option 2: Run 'start-backend.bat' and 'start-frontend.bat' separately"
    Write-Host "   Option 3: Manual start:"
    Write-Host "     - Backend: cd backend && npm run dev"
    Write-Host "     - Frontend: cd frontend && npm run dev"
    Write-Host "`n" -NoNewline
    Write-Host "🌐 Access points:" -ForegroundColor $InfoColor
    Write-Host "   • Main App: http://localhost:3000"
    Write-Host "   • Dashboard: http://localhost:3000/dashboard"
    Write-Host "   • Contact & Communication Demo: http://localhost:3000/demo/contact-communication"
    Write-Host "   • Transaction Pages: http://localhost:3000/dashboard/transactions"
    Write-Host "   • Backend API: http://localhost:3001"
    Write-Host "`n" -NoNewline
    Write-Host "🔧 Database info:" -ForegroundColor $InfoColor
    Write-Host "   • Database: tc_platform"
    Write-Host "   • User: tc_user"
    Write-Host "   • Password: $DatabasePassword"
    Write-Host "   • Host: localhost:5432"
    Write-Host "`n" -NoNewline
    Write-Host "📚 Next steps:" -ForegroundColor $InfoColor
    Write-Host "   1. Start the application using one of the methods above"
    Write-Host "   2. Open http://localhost:3000 in your browser"
    Write-Host "   3. Explore the Contact & Communication features"
    Write-Host "   4. Check out the demo at /demo/contact-communication"
    Write-Host "`n" -NoNewline
}

# Main execution
try {
    Write-Host "🚀 TC Platform Windows Setup Script" -ForegroundColor $InfoColor
    Write-Host "=====================================" -ForegroundColor $InfoColor

    # Check if running as administrator
    if (!(Test-Administrator)) {
        Write-Error "This script must be run as Administrator. Please restart PowerShell as Administrator and try again."
        exit 1
    }

    # Check if we're in the right directory
    if (!(Test-Path "backend") -or !(Test-Path "frontend")) {
        Write-Error "Please run this script from the TC Platform root directory (where backend and frontend folders are located)."
        exit 1
    }

    # Install prerequisites
    if (!$SkipPrerequisites) {
        if (!(Install-Prerequisites)) {
            Write-Error "Failed to install prerequisites. Please install Node.js and PostgreSQL manually."
            exit 1
        }
        
        # Refresh environment variables after installation
        $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
    }

    # Setup database
    if (!(Setup-Database)) {
        Write-Error "Database setup failed. Please check PostgreSQL installation."
        exit 1
    }

    # Install dependencies
    if (!(Install-Dependencies)) {
        Write-Error "Failed to install project dependencies."
        exit 1
    }

    # Create environment files
    if (!(Create-EnvironmentFiles)) {
        Write-Error "Failed to create environment files."
        exit 1
    }

    # Setup database schema
    if (!(Setup-Database-Schema)) {
        Write-Error "Failed to setup database schema."
        exit 1
    }

    # Create startup scripts
    Create-StartupScripts

    # Show completion message
    Show-CompletionMessage

    Write-Host "Setup completed successfully! 🎉" -ForegroundColor $SuccessColor
}
catch {
    Write-Error "Setup failed: $($_.Exception.Message)"
    Write-Host "Please check the error messages above and try again." -ForegroundColor $WarningColor
    exit 1
}
